import { Injectable } from '@nestjs/common';

@Injectable()
export class GroqService {
  private apiKey: string;
  private baseURL: string = 'https://api.groq.com/openai/v1';

  constructor() {
    this.apiKey = process.env.GROQ_API_KEY || '';
  }

  async generateText(prompt: string, model: string = 'llama3-8b-8192'): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Groq API key not configured');
    }

    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: 'user', content: prompt }
        ],
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`Groq API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  async generatePosts(content: string): Promise<any[]> {
    const prompt = `Generate 5 different social media posts from the following content. Return as JSON array with format [{"post": "content"}]. Content: ${content}`;
    
    const response = await this.generateText(prompt, 'llama3-70b-8192');
    
    try {
      // 尝试解析JSON响应
      const start = response.indexOf('[');
      const end = response.lastIndexOf(']') + 1;
      if (start !== -1 && end !== -1) {
        return JSON.parse(response.slice(start, end));
      }
      return [];
    } catch (e) {
      console.error('Failed to parse Groq response:', e);
      return [];
    }
  }
}
