#!/bin/bash

echo "🔄 切换云归AI服务到DeepSeek"
echo "========================"

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：验证DeepSeek API可用性${NC}"
echo "============================"

DEEPSEEK_KEY=$(grep "DEEPSEEK_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)

if [ -z "$DEEPSEEK_KEY" ]; then
    echo "❌ 未找到DeepSeek API密钥，请先配置"
    exit 1
fi

echo "🧪 测试DeepSeek API..."
DEEPSEEK_TEST=$(curl -s -w "%{http_code}" \
  -H "Authorization: Bearer $DEEPSEEK_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 5
  }' \
  https://api.deepseek.com/chat/completions \
  -o /tmp/deepseek_test.json)

if [ "$DEEPSEEK_TEST" = "200" ]; then
    echo "✅ DeepSeek API测试成功"
else
    echo "❌ DeepSeek API测试失败，状态码: $DEEPSEEK_TEST"
    cat /tmp/deepseek_test.json 2>/dev/null
    exit 1
fi

echo ""
echo -e "${BLUE}第二步：备份原始文件${NC}"
echo "======================"

BACKUP_DIR="deepseek-switch-backup-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📁 创建备份目录: $BACKUP_DIR"

# 备份需要修改的文件
cp apps/backend/src/api/routes/copilot.controller.ts "$BACKUP_DIR/"

echo "✅ 文件备份完成"

echo ""
echo -e "${BLUE}第三步：修改CopilotController${NC}"
echo "=========================="

echo "🔧 修改CopilotController以使用DeepSeek..."

# 创建新的CopilotController
cat > apps/backend/src/api/routes/copilot.controller.ts << 'EOF'
import { Logger, Controller, Get, Post, Req, Res, Query } from '@nestjs/common';
import {
  CopilotRuntime,
  OpenAIAdapter,
  copilotRuntimeNestEndpoint,
} from '@copilotkit/runtime';
import { GetOrgFromRequest } from '@gitroom/nestjs-libraries/user/org.from.request';
import { Organization } from '@prisma/client';
import { SubscriptionService } from '@gitroom/nestjs-libraries/database/prisma/subscriptions/subscription.service';

@Controller('/copilot')
export class CopilotController {
  constructor(private _subscriptionService: SubscriptionService) {}
  @Post('/chat')
  chat(@Req() req: Request, @Res() res: Response) {
    if (
      process.env.DEEPSEEK_API_KEY === undefined ||
      process.env.DEEPSEEK_API_KEY === ''
    ) {
      Logger.warn('DeepSeek API key not set, chat functionality will not work');
      return;
    }

    const copilotRuntimeHandler = copilotRuntimeNestEndpoint({
      endpoint: '/copilot/chat',
      runtime: new CopilotRuntime(),
      serviceAdapter: new OpenAIAdapter({
        apiKey: process.env.DEEPSEEK_API_KEY,
        baseURL: 'https://api.deepseek.com',
        model: 'deepseek-chat',
      }),
    });

    // @ts-ignore
    return copilotRuntimeHandler(req, res);
  }

  @Get('/credits')
  calculateCredits(
    @GetOrgFromRequest() organization: Organization,
    @Query('type') type: 'ai_images' | 'ai_videos',
  ) {
    return this._subscriptionService.checkCredits(organization, type || 'ai_images');
  }
}
EOF

echo "✅ CopilotController已更新为使用DeepSeek"

echo ""
echo -e "${BLUE}第四步：重启服务${NC}"
echo "=================="

echo "🔄 重启Docker服务..."
docker compose -f docker-compose.prod.yml down
sleep 5
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${BLUE}第五步：测试新配置${NC}"
echo "=================="

echo "🧪 测试网站访问..."
SITE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "网站状态码: $SITE_STATUS"

echo ""
echo "🧪 测试Copilot端点..."
COPILOT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "你好"}]}' \
  https://ai.guiyunai.fun/api/copilot/chat)

echo "Copilot端点状态码: $COPILOT_STATUS"

if [ "$COPILOT_STATUS" = "200" ] || [ "$COPILOT_STATUS" = "401" ]; then
    echo "✅ Copilot端点响应正常"
else
    echo "❌ Copilot端点可能有问题"
fi

echo ""
echo -e "${GREEN}切换完成！${NC}"
echo "=============="

echo ""
echo "📋 修改摘要："
echo "✅ CopilotController已切换到DeepSeek API"
echo "✅ 使用模型: deepseek-chat"
echo "✅ API端点: https://api.deepseek.com"
echo "✅ 备份文件保存在: $BACKUP_DIR"

echo ""
echo "🧪 测试步骤："
echo "1. 访问 https://ai.guiyunai.fun/launches"
echo "2. 尝试使用AI Assistant功能"
echo "3. 检查AI回复是否正常工作"

echo ""
echo "📊 DeepSeek优势："
echo "✅ 中文支持优秀"
echo "✅ 成本极低"
echo "✅ 性能良好"
echo "✅ 专为中文优化"

echo ""
echo "🔍 如果遇到问题："
echo "1. 检查DeepSeek API密钥是否有效"
echo "2. 查看应用日志: docker compose -f docker-compose.prod.yml logs postiz"
echo "3. 恢复备份: cp $BACKUP_DIR/* apps/backend/src/api/routes/"

echo ""
echo "✅ AI服务已成功切换到DeepSeek！"

# 清理临时文件
rm -f /tmp/deepseek_test.json
