#!/bin/bash

echo "🚨 Postiz 完全重建"
echo "=================="

BACKUP_DIR=${1:-"/tmp/postiz-backup-$(date +%Y%m%d_%H%M%S)"}

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo "⚠️ 警告：这将删除所有现有的Postiz容器、卷和镜像"
echo "备份目录: $BACKUP_DIR"
echo ""
read -p "确认继续重建？(yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "取消重建"
    exit 1
fi

echo ""
echo "第一步：停止并删除现有服务..."

# 停止所有服务
docker compose -f docker-compose.prod.yml down -v

# 删除相关容器
echo "🗑️ 删除Postiz相关容器..."
docker ps -a | grep postiz | awk '{print $1}' | xargs -r docker rm -f

# 删除相关镜像
echo "🗑️ 删除Postiz相关镜像..."
docker images | grep postiz | awk '{print $3}' | xargs -r docker rmi -f

# 删除相关卷
echo "🗑️ 删除Postiz相关卷..."
docker volume ls | grep postiz | awk '{print $2}' | xargs -r docker volume rm

# 清理未使用的资源
docker system prune -f

echo ""
echo "第二步：重新创建优化的Docker Compose配置..."

# 创建新的docker-compose配置
cat > docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: postiz
    restart: unless-stopped
    environment:
      # 数据库配置
      DATABASE_URL: "*************************************************************/postiz-db-local"
      REDIS_URL: "redis://postiz-redis:6379"
      
      # 基础URL配置
      MAIN_URL: "https://ai.guiyunai.fun"
      FRONTEND_URL: "https://ai.guiyunai.fun"
      NEXT_PUBLIC_BACKEND_URL: "https://ai.guiyunai.fun/api"
      
      # 文件上传配置（优化）
      STORAGE_PROVIDER: "local"
      UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      NEXT_PUBLIC_UPLOAD_URL: "https://ai.guiyunai.fun/uploads"
      
      # Next.js图片优化配置
      NEXT_PUBLIC_IMAGE_DOMAINS: "ai.guiyunai.fun"
      NEXT_PUBLIC_IMAGE_LOADER: "default"
      
      # JWT和加密
      JWT_SECRET: "your-super-secret-jwt-key-change-this"
      ENCRYPT_PASSWORD: "your-encrypt-password-change-this"
      
      # 品牌配置
      NEXT_PUBLIC_POSTIZ_OAUTH_DISPLAY_NAME: "云归.中国"
      
      # X (Twitter) 配置
      X_API_KEY: "*************************"
      X_API_SECRET: "A5MInTeREXLttl2zAeGDBYNCfVP8D6sone5JPTSd3YbGcbJZK2"
      
      # LinkedIn 配置
      LINKEDIN_CLIENT_ID: ""
      LINKEDIN_CLIENT_SECRET: ""
      
      # 其他社交媒体配置
      YOUTUBE_CLIENT_ID: ""
      YOUTUBE_CLIENT_SECRET: ""
      TIKTOK_CLIENT_ID: ""
      TIKTOK_CLIENT_SECRET: ""
      FACEBOOK_APP_ID: ""
      FACEBOOK_APP_SECRET: ""
      INSTAGRAM_APP_ID: ""
      INSTAGRAM_APP_SECRET: ""
      
    ports:
      - "5000:5000"
    volumes:
      - postiz_uploads:/uploads
    depends_on:
      - postiz-postgres
      - postiz-redis
    networks:
      - postiz-network

  postiz-postgres:
    image: postgres:15
    container_name: postiz-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: postiz-db-local
      POSTGRES_USER: postiz-user
      POSTGRES_PASSWORD: postiz-password
    volumes:
      - postiz_postgres_data:/var/lib/postgresql/data
    networks:
      - postiz-network

  postiz-redis:
    image: redis:7-alpine
    container_name: postiz-redis
    restart: unless-stopped
    volumes:
      - postiz_redis_data:/data
    networks:
      - postiz-network

volumes:
  postiz_uploads:
    driver: local
  postiz_postgres_data:
    driver: local
  postiz_redis_data:
    driver: local

networks:
  postiz-network:
    driver: bridge
EOF

echo ""
echo "第三步：启动新的服务..."

# 拉取最新镜像
docker compose -f docker-compose.prod.yml pull

# 启动服务
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo "第四步：恢复数据（如果有备份）..."

if [ -f "$BACKUP_DIR/uploads-backup.tar.gz" ]; then
    echo "📦 恢复上传文件..."
    docker cp "$BACKUP_DIR/uploads-backup.tar.gz" postiz:/tmp/
    docker compose -f docker-compose.prod.yml exec -T postiz tar xzf /tmp/uploads-backup.tar.gz -C /
    docker compose -f docker-compose.prod.yml exec -T postiz rm /tmp/uploads-backup.tar.gz
fi

if [ -f "$BACKUP_DIR/database-backup.sql" ]; then
    echo "📦 恢复数据库..."
    docker cp "$BACKUP_DIR/database-backup.sql" postiz-postgres:/tmp/
    docker compose -f docker-compose.prod.yml exec -T postiz-postgres psql -U postiz-user -d postiz-db-local -f /tmp/database-backup.sql
fi

echo ""
echo "第五步：配置文件权限..."

# 设置上传目录权限
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads
docker compose -f docker-compose.prod.yml exec -T postiz chmod -R 755 /uploads
docker compose -f docker-compose.prod.yml exec -T postiz chown -R node:node /uploads 2>/dev/null || echo "权限设置完成"

echo ""
echo "第六步：测试新部署..."

# 等待服务完全启动
sleep 15

# 检查服务状态
echo "📊 检查服务状态..."
docker compose -f docker-compose.prod.yml ps

# 测试网站访问
echo "🌐 测试网站访问..."
SITE_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "网站状态码: $SITE_CODE"

# 创建测试文件
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "rebuild test" > /uploads/rebuild-test.txt'
TEST_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/rebuild-test.txt)
echo "文件访问状态码: $TEST_CODE"

echo ""
echo "🎯 重建完成！"
echo "=================================="
echo "网站访问: $SITE_CODE"
echo "文件访问: $TEST_CODE"

if [ "$SITE_CODE" = "200" ] && [ "$TEST_CODE" = "200" ]; then
    echo "✅ 重建成功！请测试图片上传功能"
else
    echo "⚠️ 可能需要进一步配置，请检查日志"
    docker compose -f docker-compose.prod.yml logs postiz --tail=10
fi

echo ""
echo "📋 下一步操作："
echo "1. 访问 https://ai.guiyunai.fun"
echo "2. 重新配置社交媒体账户"
echo "3. 测试图片上传和显示功能"
echo "4. 如有问题，检查日志: docker compose -f docker-compose.prod.yml logs postiz"
