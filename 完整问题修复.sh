#!/bin/bash

echo "🔧 Postiz完整问题修复"
echo "===================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：修复API配置${NC}"
echo "======================"

echo "🔑 测试AI API密钥..."

# 测试GROQ API
echo "测试GROQ API..."
GROQ_RESPONSE=$(curl -s -X POST https://api.groq.com/openai/v1/chat/completions \
  -H "Authorization: Bearer ********************************************************" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "model": "llama3-8b-8192",
    "max_tokens": 10
  }' 2>/dev/null)

if echo "$GROQ_RESPONSE" | grep -q "choices"; then
    echo "✅ GROQ API正常"
else
    echo "❌ GROQ API异常"
    echo "响应: $GROQ_RESPONSE"
fi

# 测试OpenAI API
echo "测试OpenAI API..."
OPENAI_RESPONSE=$(curl -s -X POST https://api.openai.com/v1/chat/completions \
  -H "Authorization: Bearer ********************************************************************************************************************************************************************" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 10
  }' 2>/dev/null)

if echo "$OPENAI_RESPONSE" | grep -q "choices"; then
    echo "✅ OpenAI API正常"
else
    echo "❌ OpenAI API异常"
    echo "响应: $OPENAI_RESPONSE"
fi

echo ""
echo -e "${YELLOW}第二步：重启服务应用新配置${NC}"
echo "================================"

echo "🔄 重启Postiz服务..."
docker compose -f docker-compose.prod.yml down
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${GREEN}第三步：测试图片问题修复${NC}"
echo "=========================="

# 检查服务状态
echo "📊 检查服务状态..."
docker ps --format "table {{.Names}}\t{{.Status}}" | grep postiz

# 检查环境变量
echo "📋 检查关键环境变量..."
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "DISABLE_IMAGE|UPLOAD_URL|STORAGE" | head -5

# 测试网站访问
echo "🌐 测试网站访问..."
MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "网站状态码: $MAIN_CODE"

# 创建测试文件
echo "📁 创建测试文件..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads/test
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "test content" > /uploads/test/test.txt'

# 测试文件访问
echo "📄 测试文件访问..."
FILE_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test/test.txt 2>/dev/null || echo "000")
echo "文件访问状态码: $FILE_CODE"

# 测试现有图片
echo "🖼️ 测试现有图片..."
EXISTING_IMAGE=$(docker compose -f docker-compose.prod.yml exec -T postiz find /uploads -name "*.jpeg" -o -name "*.jpg" -o -name "*.png" | head -1 | tr -d '\r')

if [ ! -z "$EXISTING_IMAGE" ]; then
    RELATIVE_PATH=${EXISTING_IMAGE#/uploads/}
    echo "测试图片: $RELATIVE_PATH"
    
    IMAGE_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://ai.guiyunai.fun/uploads/$RELATIVE_PATH" 2>/dev/null || echo "000")
    echo "图片访问状态码: $IMAGE_CODE"
    
    if [ "$IMAGE_CODE" = "200" ]; then
        echo "✅ 图片可以直接访问"
    else
        echo "❌ 图片无法访问"
    fi
else
    echo "⚠️ 未找到现有图片文件"
fi

echo ""
echo -e "${BLUE}第四步：检查容器日志${NC}"
echo "======================"

echo "📝 最新日志（最后5行）:"
docker compose -f docker-compose.prod.yml logs postiz --tail=5 2>/dev/null || echo "无法获取日志"

echo ""
echo -e "${YELLOW}修复结果总结${NC}"
echo "================"

echo "网站访问: $MAIN_CODE"
echo "文件访问: $FILE_CODE"
if [ ! -z "$EXISTING_IMAGE" ]; then
    echo "图片访问: $IMAGE_CODE"
fi

echo ""
if [ "$MAIN_CODE" = "200" ] && [ "$FILE_CODE" = "200" ]; then
    echo -e "${GREEN}🎉 修复成功！${NC}"
    echo "✅ 网站正常访问"
    echo "✅ 文件上传和访问正常"
    echo "✅ 已禁用Next.js图片优化，避免404错误"
    echo ""
    echo "🎯 现在应该可以："
    echo "1. 正常上传图片"
    echo "2. 图片直接显示（不通过_next/image）"
    echo "3. Your Assistant功能正常（如果API密钥有效）"
    
elif [ "$MAIN_CODE" = "200" ]; then
    echo -e "${YELLOW}⚠️ 部分修复成功${NC}"
    echo "✅ 网站正常访问"
    echo "❌ 文件访问有问题"
    echo ""
    echo "🔧 建议："
    echo "1. 检查Docker卷挂载"
    echo "2. 考虑使用外部存储（阿里云OSS）"
    
else
    echo -e "${RED}❌ 修复失败${NC}"
    echo "❌ 网站无法访问"
    echo ""
    echo "🔧 建议："
    echo "1. 检查容器状态: docker ps"
    echo "2. 查看详细日志: docker compose -f docker-compose.prod.yml logs"
    echo "3. 检查端口5000是否被占用"
fi

echo ""
echo "📋 下一步操作："
echo "1. 访问 https://ai.guiyunai.fun"
echo "2. 测试Your Assistant功能"
echo "3. 测试图片上传和显示"
echo "4. 如果图片仍有问题，考虑配置阿里云OSS外部存储"

echo ""
echo "🔧 如果需要配置阿里云OSS："
echo "请提供您的OSS配置信息，我可以帮您配置外部存储"
