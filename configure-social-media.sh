#!/bin/bash

# 云归.中国 Postiz 社交媒体配置脚本
# 使用方法: ./configure-social-media.sh [platform] [client_id] [client_secret]

set -e

DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
BACKUP_FILE="docker-compose.prod.yml.backup.$(date +%Y%m%d_%H%M%S)"

# 创建备份
echo "📋 创建配置文件备份..."
cp "$DOCKER_COMPOSE_FILE" "$BACKUP_FILE"
echo "✅ 备份已创建: $BACKUP_FILE"

# 配置函数
configure_platform() {
    local platform=$1
    local client_id=$2
    local client_secret=$3
    
    echo "🔧 配置 $platform..."
    
    case $platform in
        "tiktok")
            sed -i "s/TIKTOK_CLIENT_ID: \"\"/TIKTOK_CLIENT_ID: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/TIKTOK_CLIENT_SECRET: \"\"/TIKTOK_CLIENT_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ TikTok 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/tiktok"
            ;;
        "youtube")
            sed -i "s/YOUTUBE_CLIENT_ID: \"\"/YOUTUBE_CLIENT_ID: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/YOUTUBE_CLIENT_SECRET: \"\"/YOUTUBE_CLIENT_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ YouTube 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/youtube"
            ;;
        "twitter"|"x")
            sed -i "s/X_API_KEY: \"\"/X_API_KEY: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/X_API_SECRET: \"\"/X_API_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ X (Twitter) 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/x"
            ;;
        "linkedin")
            sed -i "s/LINKEDIN_CLIENT_ID: \"\"/LINKEDIN_CLIENT_ID: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/LINKEDIN_CLIENT_SECRET: \"\"/LINKEDIN_CLIENT_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ LinkedIn 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/linkedin"
            ;;
        "facebook")
            sed -i "s/FACEBOOK_APP_ID: \"\"/FACEBOOK_APP_ID: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/FACEBOOK_APP_SECRET: \"\"/FACEBOOK_APP_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ Facebook 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/instagram"
            ;;
        "instagram")
            sed -i "s/INSTAGRAM_APP_ID: \"\"/INSTAGRAM_APP_ID: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/INSTAGRAM_APP_SECRET: \"\"/INSTAGRAM_APP_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ Instagram 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/instagram-standalone"
            ;;
        "reddit")
            sed -i "s/REDDIT_CLIENT_ID: \"\"/REDDIT_CLIENT_ID: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/REDDIT_CLIENT_SECRET: \"\"/REDDIT_CLIENT_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ Reddit 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/reddit"
            ;;
        "discord")
            sed -i "s/DISCORD_CLIENT_ID: \"\"/DISCORD_CLIENT_ID: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/DISCORD_CLIENT_SECRET: \"\"/DISCORD_CLIENT_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ Discord 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/discord"
            ;;
        "pinterest")
            sed -i "s/PINTEREST_CLIENT_ID: \"\"/PINTEREST_CLIENT_ID: \"$client_id\"/" "$DOCKER_COMPOSE_FILE"
            sed -i "s/PINTEREST_CLIENT_SECRET: \"\"/PINTEREST_CLIENT_SECRET: \"$client_secret\"/" "$DOCKER_COMPOSE_FILE"
            echo "✅ Pinterest 配置完成"
            echo "📝 回调URL: https://ai.guiyunai.fun/integrations/social/pinterest"
            ;;
        *)
            echo "❌ 不支持的平台: $platform"
            echo "📋 支持的平台: tiktok, youtube, twitter, linkedin, facebook, instagram, reddit, discord, pinterest"
            exit 1
            ;;
    esac
}

# 重启服务函数
restart_services() {
    echo "🔄 重启 Postiz 服务..."
    docker compose -f "$DOCKER_COMPOSE_FILE" down
    docker compose -f "$DOCKER_COMPOSE_FILE" up -d
    echo "✅ 服务重启完成"
    echo "🌐 请访问: https://ai.guiyunai.fun"
}

# 显示帮助信息
show_help() {
    echo "🚀 云归.中国 Postiz 社交媒体配置脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [platform] [client_id] [client_secret]"
    echo ""
    echo "支持的平台:"
    echo "  tiktok     - TikTok"
    echo "  youtube    - YouTube"
    echo "  twitter/x  - X (Twitter)"
    echo "  linkedin   - LinkedIn"
    echo "  facebook   - Facebook"
    echo "  instagram  - Instagram"
    echo "  reddit     - Reddit"
    echo "  discord    - Discord"
    echo "  pinterest  - Pinterest"
    echo ""
    echo "示例:"
    echo "  $0 tiktok your_client_id your_client_secret"
    echo "  $0 youtube your_google_client_id your_google_client_secret"
    echo ""
    echo "特殊命令:"
    echo "  $0 restart  - 仅重启服务"
    echo "  $0 backup   - 创建配置备份"
    echo "  $0 restore  - 恢复最新备份"
}

# 恢复备份函数
restore_backup() {
    local latest_backup=$(ls -t docker-compose.prod.yml.backup.* 2>/dev/null | head -n1)
    if [ -n "$latest_backup" ]; then
        echo "📋 恢复备份: $latest_backup"
        cp "$latest_backup" "$DOCKER_COMPOSE_FILE"
        echo "✅ 备份恢复完成"
        restart_services
    else
        echo "❌ 未找到备份文件"
        exit 1
    fi
}

# 主逻辑
case "${1:-help}" in
    "help"|"-h"|"--help")
        show_help
        ;;
    "restart")
        restart_services
        ;;
    "backup")
        echo "✅ 备份已创建: $BACKUP_FILE"
        ;;
    "restore")
        restore_backup
        ;;
    *)
        if [ $# -eq 3 ]; then
            configure_platform "$1" "$2" "$3"
            echo ""
            echo "🔄 是否立即重启服务以应用配置? (y/N)"
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                restart_services
            else
                echo "⚠️  请手动运行以下命令重启服务:"
                echo "   $0 restart"
            fi
        else
            echo "❌ 参数错误"
            show_help
            exit 1
        fi
        ;;
esac
