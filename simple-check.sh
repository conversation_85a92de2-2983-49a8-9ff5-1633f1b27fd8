#!/bin/bash

echo "=== 简单检查脚本 ==="

cd /www/wwwroot/ai.guiyunai.fun

echo "1. Docker容器状态："
docker ps --format "{{.Names}} {{.Status}}" | grep postiz

echo ""
echo "2. Docker卷："
docker volume ls | grep postiz

echo ""
echo "3. 端口检查："
ss -tlnp | grep :5000
ss -tlnp | grep :80
ss -tlnp | grep :443

echo ""
echo "4. Nginx状态："
systemctl is-active nginx

echo ""
echo "5. 测试文件创建："
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads/test
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "test" > /uploads/test/simple.txt'

echo ""
echo "6. 测试文件访问："
echo "容器内："
docker compose -f docker-compose.prod.yml exec -T postiz ls -la /uploads/test/

echo ""
echo "HTTP测试："
curl -s -o /dev/null -w "本地5000: %{http_code}\n" http://localhost:5000/uploads/test/simple.txt
curl -s -o /dev/null -w "外部HTTPS: %{http_code}\n" https://ai.guiyunai.fun/uploads/test/simple.txt

echo ""
echo "7. 网站主页："
curl -s -o /dev/null -w "主页状态: %{http_code}\n" https://ai.guiyunai.fun

echo ""
echo "=== 检查完成 ==="
