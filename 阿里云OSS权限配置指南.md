# 🔒 阿里云OSS权限配置指南

## ❗ 重要说明

阿里云已禁用公共读写权限，但**不会影响功能**！我们有更好的解决方案。

## 🎯 推荐配置方案

### 方案一：私有Bucket + Bucket策略（推荐）

1. **创建Bucket时选择"私有"权限**
2. **配置Bucket策略实现公共读取**

#### 步骤详解：

1. **登录阿里云OSS控制台**
   - 访问：https://oss.console.aliyun.com/

2. **创建Bucket**
   ```
   Bucket名称：postiz-images-2025
   地域：华东1-杭州 (oss-cn-hangzhou)
   存储类型：标准存储
   读写权限：私有 ✅
   ```

3. **配置Bucket策略**
   - 进入您的Bucket → 权限管理 → Bucket授权策略
   - 点击"新增授权"
   - 选择"自定义策略"
   - 复制以下策略：

```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": [
        "oss:GetObject"
      ],
      "Resource": [
        "acs:oss:*:*:您的bucket名称/*"
      ]
    }
  ]
}
```

### 方案二：CDN加速（最佳实践）

使用阿里云CDN可以：
- ✅ 解决公共访问问题
- ✅ 加速图片加载速度
- ✅ 降低OSS流量费用
- ✅ 提供HTTPS访问
- ✅ 防盗链保护

#### CDN配置步骤：

1. **开通CDN服务**
   - 访问：https://cdn.console.aliyun.com/

2. **添加加速域名**
   ```
   加速域名：img.guiyunai.fun
   业务类型：图片小文件
   源站类型：OSS域名
   源站地址：您的bucket域名
   ```

3. **配置CNAME解析**
   - 在域名解析中添加CNAME记录
   - 将 img.guiyunai.fun 指向CDN分配的域名

## 🔧 更新配置

使用我们的配置脚本时，选择以下配置：

```bash
# 使用私有Bucket
./configure-aliyun-oss.sh --interactive

# 配置时选择：
读写权限：私有
```

## 📊 权限对比

| 配置方式 | 安全性 | 性能 | 费用 | 推荐度 |
|---------|--------|------|------|--------|
| 公共读写 | ❌ 低 | ⭐⭐⭐ | 💰 中 | ❌ 已禁用 |
| 私有+策略 | ✅ 高 | ⭐⭐⭐ | 💰 中 | ✅ 推荐 |
| 私有+CDN | ✅ 最高 | ⭐⭐⭐⭐⭐ | 💰 低 | 🌟 最佳 |

## 🚀 快速配置命令

```bash
# 1. 进入项目目录
cd /www/wwwroot/ai.guiyunai.fun

# 2. 运行配置脚本
./configure-aliyun-oss.sh --interactive

# 3. 按提示输入信息（选择私有权限）
```

## 🔍 验证配置

配置完成后，验证以下几点：

1. **Bucket访问测试**
   ```bash
   curl -I https://您的bucket名称.oss-cn-hangzhou.aliyuncs.com/test.jpg
   ```

2. **应用上传测试**
   - 登录 https://ai.guiyunai.fun
   - 尝试上传图片
   - 检查图片是否正常显示

3. **权限检查**
   - OSS控制台 → 您的Bucket → 权限管理
   - 确认权限为"私有"
   - 确认Bucket策略已生效

## 💡 常见问题

### Q: 为什么不能选择公共读写？
A: 阿里云为了安全考虑，已禁用公共读写权限。私有+策略的方式更安全。

### Q: 私有权限会影响图片显示吗？
A: 不会！通过Bucket策略，外部仍可正常访问图片。

### Q: CDN是否必需？
A: 不是必需的，但强烈推荐。CDN可以显著提升访问速度并降低费用。

### Q: 如何查看当前权限配置？
A: OSS控制台 → 您的Bucket → 基础设置 → 读写权限

## 📞 技术支持

如遇问题，请检查：
1. Bucket策略是否正确配置
2. 域名解析是否正确
3. 防火墙是否允许OSS访问

---

**记住**：私有权限 + 正确的策略配置 = 安全且功能完整的图片存储！
