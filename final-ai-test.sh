#!/bin/bash

echo "🎯 云归AI功能最终测试"
echo "===================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：检查服务状态${NC}"
echo "======================"

echo "🔍 检查Docker容器状态..."
CONTAINER_STATUS=$(docker ps --format "{{.Names}} {{.Status}}" | grep postiz)
if echo "$CONTAINER_STATUS" | grep -q "Up"; then
    echo "✅ Postiz容器运行正常"
    echo "状态: $CONTAINER_STATUS"
else
    echo "❌ Postiz容器未运行"
    echo "尝试启动服务..."
    docker compose -f docker-compose.prod.yml up -d
    sleep 20
fi

echo ""
echo "🌐 检查网站访问..."
SITE_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun --connect-timeout 10)
echo "网站状态码: $SITE_CODE"

if [ "$SITE_CODE" = "200" ]; then
    echo "✅ 网站访问正常"
else
    echo "❌ 网站访问异常"
fi

echo ""
echo -e "${BLUE}第二步：检查AI配置${NC}"
echo "===================="

echo "🔍 检查当前AI配置..."
if grep -q "GROQ_API_KEY" apps/backend/src/api/routes/copilot.controller.ts; then
    echo "✅ 当前使用Groq AI服务"
    AI_SERVICE="Groq"
elif grep -q "DEEPSEEK_API_KEY" apps/backend/src/api/routes/copilot.controller.ts; then
    echo "✅ 当前使用DeepSeek AI服务"
    AI_SERVICE="DeepSeek"
elif grep -q "OPENAI_API_KEY" apps/backend/src/api/routes/copilot.controller.ts; then
    echo "⚠️ 当前使用OpenAI服务"
    AI_SERVICE="OpenAI"
else
    echo "❓ AI配置不明确"
    AI_SERVICE="Unknown"
fi

echo ""
echo -e "${BLUE}第三步：测试AI API连接${NC}"
echo "======================"

if [ "$AI_SERVICE" = "Groq" ]; then
    echo "🧪 测试Groq API..."
    GROQ_KEY=$(grep "GROQ_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
    
    GROQ_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
      -H "Authorization: Bearer $GROQ_KEY" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "llama3-8b-8192",
        "messages": [{"role": "user", "content": "你好，请简短回复"}],
        "max_tokens": 20
      }' \
      https://api.groq.com/openai/v1/chat/completions)
    
    GROQ_CODE=$(echo $GROQ_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    GROQ_BODY=$(echo $GROQ_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$GROQ_CODE" = "200" ]; then
        echo "✅ Groq API连接成功"
        AI_REPLY=$(echo $GROQ_BODY | jq -r '.choices[0].message.content' 2>/dev/null || echo "解析失败")
        echo "AI回复: $AI_REPLY"
    else
        echo "❌ Groq API连接失败，状态码: $GROQ_CODE"
        echo "错误: $GROQ_BODY"
    fi

elif [ "$AI_SERVICE" = "DeepSeek" ]; then
    echo "🧪 测试DeepSeek API..."
    DEEPSEEK_KEY=$(grep "DEEPSEEK_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
    
    DEEPSEEK_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
      -H "Authorization: Bearer $DEEPSEEK_KEY" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "deepseek-chat",
        "messages": [{"role": "user", "content": "你好，请简短回复"}],
        "max_tokens": 20
      }' \
      https://api.deepseek.com/chat/completions)
    
    DEEPSEEK_CODE=$(echo $DEEPSEEK_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    DEEPSEEK_BODY=$(echo $DEEPSEEK_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$DEEPSEEK_CODE" = "200" ]; then
        echo "✅ DeepSeek API连接成功"
        AI_REPLY=$(echo $DEEPSEEK_BODY | jq -r '.choices[0].message.content' 2>/dev/null || echo "解析失败")
        echo "AI回复: $AI_REPLY"
    else
        echo "❌ DeepSeek API连接失败，状态码: $DEEPSEEK_CODE"
        echo "错误: $DEEPSEEK_BODY"
    fi
fi

echo ""
echo -e "${BLUE}第四步：测试应用AI端点${NC}"
echo "========================"

echo "🧪 测试Copilot Chat端点..."
COPILOT_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "Hello"}]}' \
  https://ai.guiyunai.fun/api/copilot/chat)

COPILOT_CODE=$(echo $COPILOT_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')

if [ "$COPILOT_CODE" = "200" ]; then
    echo "✅ Copilot端点正常工作"
elif [ "$COPILOT_CODE" = "401" ]; then
    echo "⚠️ Copilot端点需要认证（这是正常的）"
elif [ "$COPILOT_CODE" = "500" ]; then
    echo "❌ Copilot端点服务器错误"
else
    echo "❌ Copilot端点异常，状态码: $COPILOT_CODE"
fi

echo ""
echo -e "${GREEN}测试总结${NC}"
echo "=========="

echo ""
echo "📊 系统状态："
echo "网站访问: $([ "$SITE_CODE" = "200" ] && echo "✅ 正常" || echo "❌ 异常")"
echo "AI服务: $AI_SERVICE"
echo "API连接: $([ "$GROQ_CODE" = "200" ] || [ "$DEEPSEEK_CODE" = "200" ] && echo "✅ 正常" || echo "❌ 异常")"
echo "Copilot端点: $([ "$COPILOT_CODE" = "200" ] || [ "$COPILOT_CODE" = "401" ] && echo "✅ 正常" || echo "❌ 异常")"

echo ""
echo "🎯 功能状态："
if [ "$SITE_CODE" = "200" ] && ([ "$GROQ_CODE" = "200" ] || [ "$DEEPSEEK_CODE" = "200" ]) && ([ "$COPILOT_CODE" = "200" ] || [ "$COPILOT_CODE" = "401" ]); then
    echo "🎉 AI Assistant功能应该正常工作！"
    echo ""
    echo "✅ 所有测试通过，可以使用AI功能"
    echo ""
    echo "🧪 手动测试步骤："
    echo "1. 访问 https://ai.guiyunai.fun/launches"
    echo "2. 点击AI Assistant功能"
    echo "3. 输入消息测试AI回复"
    echo "4. 检查是否还出现'An error occurred'错误"
else
    echo "⚠️ 发现问题，需要进一步排查"
    echo ""
    echo "🔧 建议的修复步骤："
    
    if [ "$SITE_CODE" != "200" ]; then
        echo "1. 检查网站服务: docker compose -f docker-compose.prod.yml restart"
    fi
    
    if [ "$GROQ_CODE" != "200" ] && [ "$DEEPSEEK_CODE" != "200" ]; then
        echo "2. 检查AI API密钥配置"
        echo "3. 验证API额度是否充足"
    fi
    
    if [ "$COPILOT_CODE" != "200" ] && [ "$COPILOT_CODE" != "401" ]; then
        echo "4. 检查应用日志: docker compose -f docker-compose.prod.yml logs postiz"
    fi
fi

echo ""
echo "📞 如需帮助："
echo "如果问题持续存在，请提供："
echo "- 浏览器开发者工具中的错误信息"
echo "- 应用日志中的相关错误"
echo "- 具体的错误消息截图"

echo ""
echo "🔗 AI服务管理："
echo "使用 ./ai-service-manager.sh 可以切换不同的AI服务"

echo ""
echo "✅ 测试完成！"
