#!/bin/bash

echo "🔍 诊断502错误 - 云归应用"
echo "========================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：检查Docker服务${NC}"
echo "======================"

echo "Docker版本："
docker --version
echo ""

echo "Docker Compose版本："
docker compose version
echo ""

echo "Docker服务状态："
systemctl is-active docker
echo ""

echo -e "${BLUE}第二步：检查配置文件${NC}"
echo "======================"

echo "检查docker-compose.prod.yml语法："
if docker compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 配置文件语法正确${NC}"
else
    echo -e "${RED}❌ 配置文件语法错误${NC}"
    docker compose -f docker-compose.prod.yml config
fi
echo ""

echo -e "${BLUE}第三步：检查容器状态${NC}"
echo "======================"

echo "所有容器状态："
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo ""

echo "Postiz相关容器："
docker ps -a | grep postiz || echo "未找到postiz容器"
echo ""

echo -e "${BLUE}第四步：检查端口占用${NC}"
echo "======================"

echo "检查5000端口："
if netstat -tlnp | grep :5000; then
    echo "端口5000被占用"
else
    echo "端口5000未被占用"
fi
echo ""

echo "检查3000端口："
if netstat -tlnp | grep :3000; then
    echo "端口3000被占用"
else
    echo "端口3000未被占用"
fi
echo ""

echo -e "${BLUE}第五步：检查服务日志${NC}"
echo "======================"

echo "Postiz应用日志（最后20行）："
if docker compose -f docker-compose.prod.yml logs postiz --tail=20 2>/dev/null; then
    echo "日志获取成功"
else
    echo -e "${RED}无法获取Postiz日志${NC}"
fi
echo ""

echo "数据库日志（最后10行）："
if docker compose -f docker-compose.prod.yml logs postiz-postgres --tail=10 2>/dev/null; then
    echo "数据库日志获取成功"
else
    echo -e "${RED}无法获取数据库日志${NC}"
fi
echo ""

echo "Redis日志（最后10行）："
if docker compose -f docker-compose.prod.yml logs postiz-redis --tail=10 2>/dev/null; then
    echo "Redis日志获取成功"
else
    echo -e "${RED}无法获取Redis日志${NC}"
fi
echo ""

echo -e "${BLUE}第六步：测试网络连接${NC}"
echo "======================"

echo "测试本地5000端口连接："
if curl -s --connect-timeout 5 http://localhost:5000 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 本地5000端口可访问${NC}"
else
    echo -e "${RED}❌ 本地5000端口不可访问${NC}"
fi

echo "测试外部域名连接："
RESPONSE_CODE=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "外部访问状态码: $RESPONSE_CODE"

if [ "$RESPONSE_CODE" = "200" ]; then
    echo -e "${GREEN}✅ 外部访问正常${NC}"
elif [ "$RESPONSE_CODE" = "502" ]; then
    echo -e "${RED}❌ 502错误 - 后端服务不可用${NC}"
else
    echo -e "${YELLOW}⚠️ 其他状态码: $RESPONSE_CODE${NC}"
fi
echo ""

echo -e "${BLUE}第七步：检查Nginx配置${NC}"
echo "======================"

echo "Nginx配置测试："
if nginx -t 2>/dev/null; then
    echo -e "${GREEN}✅ Nginx配置正确${NC}"
else
    echo -e "${RED}❌ Nginx配置错误${NC}"
    nginx -t
fi
echo ""

echo "Nginx服务状态："
systemctl is-active nginx
echo ""

echo -e "${BLUE}第八步：修复建议${NC}"
echo "=================="

if [ "$RESPONSE_CODE" = "502" ]; then
    echo -e "${YELLOW}502错误通常由以下原因引起：${NC}"
    echo "1. 后端应用未启动或启动失败"
    echo "2. 端口配置错误"
    echo "3. 数据库连接问题"
    echo "4. 环境变量配置错误"
    echo ""
    echo -e "${GREEN}建议的修复步骤：${NC}"
    echo "1. 重启所有服务: docker compose -f docker-compose.prod.yml restart"
    echo "2. 检查应用日志: docker compose -f docker-compose.prod.yml logs postiz -f"
    echo "3. 检查数据库连接: docker compose -f docker-compose.prod.yml exec postiz-postgres pg_isready"
    echo "4. 如果问题持续，尝试完全重建: docker compose -f docker-compose.prod.yml down && docker compose -f docker-compose.prod.yml up -d"
fi

echo ""
echo -e "${GREEN}诊断完成！${NC}"
