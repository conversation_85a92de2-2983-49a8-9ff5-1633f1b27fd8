# 🎯 各平台详细配置步骤

## 🚨 当前问题解决方案

### 问题分析
根据您的错误截图：
1. **TikTok错误**: "client_key" 缺失 - 需要配置 `TIKTOK_CLIENT_ID` 和 `TIKTOK_CLIENT_SECRET`
2. **YouTube错误**: "Missing required parameter: client_id" - 需要配置 `YOUTUBE_CLIENT_ID` 和 `YOUTUBE_CLIENT_SECRET`

### 立即解决步骤

**第一步：检查服务状态**
```bash
cd /www/wwwroot/ai.guiyunai.fun
docker compose -f docker-compose.prod.yml ps
```

**第二步：查看日志（如有问题）**
```bash
docker compose -f docker-compose.prod.yml logs postiz
```

## 📱 平台配置优先级建议

### 🟢 立即可配置（无需复杂审核）
1. **Bluesky** - 最简单，直接用户名密码
2. **Mastodon** - 选择实例，简单OAuth
3. **Reddit** - 创建应用即可
4. **Discord** - 创建Bot应用

### 🟡 中等难度（需要开发者账户）
5. **X (Twitter)** - 需要开发者账户
6. **LinkedIn** - 需要公司页面
7. **Pinterest** - 需要商业账户

### 🔴 高难度（需要审核和复杂配置）
8. **YouTube** - Google OAuth，多个API
9. **TikTok** - 开发者账户，HTTPS要求
10. **Instagram/Facebook** - Meta Business验证

## 🛠️ 详细配置步骤

### 1. Bluesky（推荐首先配置）

**优势**: 无需API密钥，直接登录
**步骤**:
1. 注册Bluesky账户: https://bsky.app
2. 在Postiz中点击"Add Channel" > "Bluesky"
3. 输入用户名和密码即可

### 2. Reddit 配置

**步骤1**: 创建Reddit应用
```
1. 访问: https://www.reddit.com/prefs/apps
2. 点击"Create App"或"Create Another App"
3. 填写信息:
   - Name: 云归中国Postiz
   - App type: web app
   - Redirect URI: https://ai.guiyunai.fun/integrations/social/reddit
```

**步骤2**: 配置环境变量
```bash
./configure-social-media.sh reddit "你的client_id" "你的client_secret"
```

### 3. Discord 配置

**步骤1**: 创建Discord应用
```
1. 访问: https://discord.com/developers/applications
2. 点击"New Application"
3. 填写应用名称: 云归中国Postiz
4. 在OAuth2设置中添加重定向URL:
   https://ai.guiyunai.fun/integrations/social/discord
```

**步骤2**: 获取Bot令牌
```
1. 在"Bot"页面创建Bot
2. 复制Bot Token
3. 在OAuth2页面复制Client ID和Client Secret
```

**步骤3**: 配置环境变量
```bash
./configure-social-media.sh discord "你的client_id" "你的client_secret"
```

### 4. X (Twitter) 配置

**前提条件**: Twitter开发者账户

**步骤1**: 创建Twitter应用
```
1. 访问: https://developer.twitter.com/en/portal/dashboard
2. 创建新项目和应用
3. 在应用设置中配置:
   - App permissions: Read and Write
   - Type of App: Web App, Automated App or Bot
   - Callback URI: https://ai.guiyunai.fun/integrations/social/x
```

**步骤2**: 获取API密钥
```
1. 在"Keys and Tokens"页面
2. 复制API Key和API Key Secret
```

**步骤3**: 配置环境变量
```bash
./configure-social-media.sh twitter "你的api_key" "你的api_secret"
```

### 5. LinkedIn 配置

**步骤1**: 创建LinkedIn应用
```
1. 访问: https://www.linkedin.com/developers/apps
2. 创建新应用
3. 添加产品:
   - Sign In with LinkedIn
   - Share on LinkedIn
   - Marketing Developer Platform
4. 设置重定向URL:
   https://ai.guiyunai.fun/integrations/social/linkedin
```

**步骤2**: 配置环境变量
```bash
./configure-social-media.sh linkedin "你的client_id" "你的client_secret"
```

### 6. YouTube 配置（解决您的当前问题）

**步骤1**: 创建Google Cloud项目
```
1. 访问: https://console.cloud.google.com/
2. 创建新项目: "云归中国Postiz"
3. 启用API:
   - YouTube Data API v3
   - YouTube Analytics API
   - YouTube Reporting API
```

**步骤2**: 配置OAuth同意屏幕
```
1. 选择"外部"用户类型
2. 填写应用信息
3. 添加作用域:
   - https://www.googleapis.com/auth/youtube
   - https://www.googleapis.com/auth/youtube.upload
4. 添加测试用户（您的Google账户）
```

**步骤3**: 创建OAuth凭据
```
1. 凭据 > 创建凭据 > OAuth客户端ID
2. 应用类型: Web应用
3. 授权重定向URI: https://ai.guiyunai.fun/integrations/social/youtube
```

**步骤4**: 配置环境变量
```bash
./configure-social-media.sh youtube "你的google_client_id" "你的google_client_secret"
```

### 7. TikTok 配置（解决您的当前问题）

**前提条件**: 
- TikTok开发者账户
- 公开的隐私政策和服务条款页面

**步骤1**: 创建TikTok应用
```
1. 访问: https://developers.tiktok.com/apps
2. 创建新应用:
   - App Name: 云归中国Postiz
   - 平台: Web
   - Redirect URI: https://ai.guiyunai.fun/integrations/social/tiktok
```

**步骤2**: 添加产品和权限
```
1. 添加产品:
   - Login Kit
   - Content Posting API
2. 设置权限:
   - user.info.basic
   - video.create
   - video.publish
   - video.upload
   - user.info.profile
```

**步骤3**: 配置环境变量
```bash
./configure-social-media.sh tiktok "你的client_id" "你的client_secret"
```

## 🔧 使用配置脚本

### 基本用法
```bash
# 配置单个平台
./configure-social-media.sh [platform] [client_id] [client_secret]

# 查看帮助
./configure-social-media.sh help

# 重启服务
./configure-social-media.sh restart

# 恢复备份
./configure-social-media.sh restore
```

### 示例命令
```bash
# 配置TikTok
./configure-social-media.sh tiktok "1234567890123456" "12345678901234567890123456789012"

# 配置YouTube
./configure-social-media.sh youtube "your-google-client-id" "your-google-client-secret"

# 配置Twitter
./configure-social-media.sh twitter "your-api-key" "your-api-secret"
```

## 📋 配置检查清单

### 配置前检查
- [ ] 确保域名 https://ai.guiyunai.fun 可正常访问
- [ ] 确保Postiz服务正常运行
- [ ] 准备好各平台的开发者账户

### 配置后验证
- [ ] 重启服务成功
- [ ] 在Postiz界面中能看到新平台
- [ ] 点击连接不再出现错误
- [ ] 能够成功授权并连接账户

### 故障排除
```bash
# 查看服务状态
docker compose -f docker-compose.prod.yml ps

# 查看日志
docker compose -f docker-compose.prod.yml logs postiz

# 重启服务
./configure-social-media.sh restart
```

## 🎯 推荐配置顺序

1. **Bluesky** - 测试基本功能
2. **Reddit** - 验证OAuth流程
3. **Discord** - 测试Bot集成
4. **X (Twitter)** - 主要社交平台
5. **LinkedIn** - 商业用途
6. **YouTube** - 视频内容
7. **TikTok** - 短视频内容
8. **Instagram/Facebook** - 图片内容

按此顺序配置可以逐步验证系统功能，遇到问题时更容易定位和解决。
