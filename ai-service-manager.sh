#!/bin/bash

echo "🤖 云归AI服务管理器"
echo "=================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 显示菜单
show_menu() {
    echo ""
    echo -e "${BLUE}请选择AI服务：${NC}"
    echo "1. 🚀 Groq (推荐 - 免费额度大，速度快)"
    echo "2. 🇨🇳 DeepSeek (中文友好，成本低)"
    echo "3. 🧪 测试所有AI服务"
    echo "4. 📊 查看当前配置"
    echo "5. 🔄 恢复OpenAI配置"
    echo "6. ❌ 退出"
    echo ""
    read -p "请输入选择 (1-6): " choice
}

# 测试所有AI服务
test_all_services() {
    echo ""
    echo -e "${BLUE}测试所有AI服务...${NC}"
    echo "===================="
    
    ./test-alternative-ai.sh
}

# 查看当前配置
show_current_config() {
    echo ""
    echo -e "${BLUE}当前AI配置：${NC}"
    echo "=============="
    
    OPENAI_KEY=$(grep "OPENAI_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
    GROQ_KEY=$(grep "GROQ_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
    DEEPSEEK_KEY=$(grep "DEEPSEEK_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
    
    echo "OpenAI: ${OPENAI_KEY:0:15}... (长度: ${#OPENAI_KEY})"
    echo "Groq: ${GROQ_KEY:0:15}... (长度: ${#GROQ_KEY})"
    echo "DeepSeek: ${DEEPSEEK_KEY:0:15}... (长度: ${#DEEPSEEK_KEY})"
    
    echo ""
    echo "当前CopilotController配置："
    if grep -q "GROQ_API_KEY" apps/backend/src/api/routes/copilot.controller.ts; then
        echo "✅ 使用Groq API"
    elif grep -q "DEEPSEEK_API_KEY" apps/backend/src/api/routes/copilot.controller.ts; then
        echo "✅ 使用DeepSeek API"
    elif grep -q "OPENAI_API_KEY" apps/backend/src/api/routes/copilot.controller.ts; then
        echo "✅ 使用OpenAI API"
    else
        echo "❓ 配置不明确"
    fi
}

# 切换到Groq
switch_to_groq() {
    echo ""
    echo -e "${YELLOW}切换到Groq服务...${NC}"
    
    GROQ_KEY=$(grep "GROQ_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
    if [ -z "$GROQ_KEY" ]; then
        echo "❌ 未找到Groq API密钥"
        echo "请先在docker-compose.prod.yml中配置GROQ_API_KEY"
        echo "获取密钥: https://console.groq.com/keys"
        return 1
    fi
    
    ./switch-to-groq.sh
}

# 切换到DeepSeek
switch_to_deepseek() {
    echo ""
    echo -e "${YELLOW}切换到DeepSeek服务...${NC}"
    
    DEEPSEEK_KEY=$(grep "DEEPSEEK_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
    if [ -z "$DEEPSEEK_KEY" ]; then
        echo "❌ 未找到DeepSeek API密钥"
        echo "请先在docker-compose.prod.yml中配置DEEPSEEK_API_KEY"
        echo "获取密钥: https://platform.deepseek.com/api_keys"
        return 1
    fi
    
    ./switch-to-deepseek.sh
}

# 恢复OpenAI配置
restore_openai() {
    echo ""
    echo -e "${YELLOW}恢复OpenAI配置...${NC}"
    
    # 查找最新的备份
    LATEST_BACKUP=$(ls -t *backup*/copilot.controller.ts 2>/dev/null | head -1)
    
    if [ -n "$LATEST_BACKUP" ]; then
        echo "📁 找到备份文件: $LATEST_BACKUP"
        cp "$LATEST_BACKUP" apps/backend/src/api/routes/copilot.controller.ts
        echo "✅ 已恢复OpenAI配置"
        
        echo "🔄 重启服务..."
        docker compose -f docker-compose.prod.yml restart
        echo "✅ 服务已重启"
    else
        echo "❌ 未找到备份文件"
        echo "请手动恢复或重新配置"
    fi
}

# 主循环
while true; do
    show_menu
    
    case $choice in
        1)
            switch_to_groq
            ;;
        2)
            switch_to_deepseek
            ;;
        3)
            test_all_services
            ;;
        4)
            show_current_config
            ;;
        5)
            restore_openai
            ;;
        6)
            echo "👋 退出AI服务管理器"
            exit 0
            ;;
        *)
            echo "❌ 无效选择，请输入1-6"
            ;;
    esac
    
    echo ""
    read -p "按Enter键继续..."
done
