#!/bin/bash

echo "🧪 测试Postiz图片修复结果"
echo "=========================="

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo "第一步：检查服务状态"
echo "==================="

# 检查容器状态
echo "📊 检查容器状态..."
docker ps --format "table {{.Names}}\t{{.Status}}" | grep postiz

echo ""
echo "📋 检查环境变量..."
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "STORAGE|UPLOAD|STATIC" | head -10

echo ""
echo "第二步：测试文件访问"
echo "=================="

# 创建测试文件
echo "📁 创建测试文件..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads/test
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "test image content" > /uploads/test/test.txt'
docker compose -f docker-compose.prod.yml exec -T postiz chmod 644 /uploads/test/test.txt

# 测试直接文件访问
echo "🌐 测试直接文件访问..."
DIRECT_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test/test.txt 2>/dev/null || echo "000")
echo "直接访问状态码: $DIRECT_CODE"

if [ "$DIRECT_CODE" = "200" ]; then
    echo "✅ 直接文件访问正常"
    echo "📖 文件内容:"
    curl -s https://ai.guiyunai.fun/uploads/test/test.txt 2>/dev/null | head -1
else
    echo "❌ 直接文件访问失败"
fi

# 测试API路由
echo ""
echo "🔗 测试API路由..."
API_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/api/uploads/test/test.txt 2>/dev/null || echo "000")
echo "API路由状态码: $API_CODE"

echo ""
echo "第三步：测试现有图片文件"
echo "======================"

# 查找现有图片文件
echo "🔍 查找现有图片文件..."
EXISTING_IMAGE=$(docker compose -f docker-compose.prod.yml exec -T postiz find /uploads -name "*.jpeg" -o -name "*.jpg" -o -name "*.png" | head -1 | tr -d '\r')

if [ ! -z "$EXISTING_IMAGE" ]; then
    RELATIVE_PATH=${EXISTING_IMAGE#/uploads/}
    echo "测试现有图片: $RELATIVE_PATH"
    
    # 测试直接访问
    EXISTING_DIRECT=$(curl -s -o /dev/null -w "%{http_code}" "https://ai.guiyunai.fun/uploads/$RELATIVE_PATH" 2>/dev/null || echo "000")
    echo "现有图片直接访问: $EXISTING_DIRECT"
    
    # 测试Next.js优化访问
    ENCODED_URL=$(python3 -c "import urllib.parse; print(urllib.parse.quote('https://ai.guiyunai.fun/uploads/$RELATIVE_PATH', safe=''))" 2>/dev/null || echo "")
    if [ ! -z "$ENCODED_URL" ]; then
        NEXTJS_URL="https://ai.guiyunai.fun/_next/image?url=$ENCODED_URL&w=128&q=75"
        NEXTJS_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$NEXTJS_URL" 2>/dev/null || echo "000")
        echo "Next.js优化访问: $NEXTJS_CODE"
    else
        echo "⚠️ 无法编码URL进行Next.js测试"
    fi
else
    echo "❌ 未找到现有图片文件"
fi

echo ""
echo "第四步：测试网站主页"
echo "=================="

MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "主页访问状态码: $MAIN_CODE"

echo ""
echo "🎯 测试结果总结"
echo "================"

echo "主页访问: $MAIN_CODE"
echo "直接文件访问: $DIRECT_CODE"
echo "API路由访问: $API_CODE"

if [ ! -z "$EXISTING_IMAGE" ]; then
    echo "现有图片直接访问: $EXISTING_DIRECT"
    if [ ! -z "$NEXTJS_CODE" ]; then
        echo "Next.js优化访问: $NEXTJS_CODE"
    fi
fi

echo ""
if [ "$MAIN_CODE" = "200" ] && [ "$DIRECT_CODE" = "200" ]; then
    if [ ! -z "$NEXTJS_CODE" ] && [ "$NEXTJS_CODE" = "200" ]; then
        echo "🎉 完全修复成功！"
        echo "✅ 主页正常"
        echo "✅ 文件访问正常"
        echo "✅ Next.js图片优化正常"
        echo ""
        echo "现在可以正常使用图片上传功能了！"
    elif [ ! -z "$NEXTJS_CODE" ] && [ "$NEXTJS_CODE" != "200" ]; then
        echo "⚠️ 部分修复成功"
        echo "✅ 主页正常"
        echo "✅ 文件访问正常"
        echo "❌ Next.js图片优化仍有问题"
        echo ""
        echo "建议：图片可以上传和直接访问，但可能在某些界面显示异常"
    else
        echo "✅ 基本修复成功"
        echo "✅ 主页正常"
        echo "✅ 文件访问正常"
        echo ""
        echo "图片上传和显示应该正常工作"
    fi
else
    echo "❌ 修复未完成"
    echo "需要进一步检查配置或考虑使用外部存储"
fi

echo ""
echo "📋 下一步操作："
echo "1. 访问 https://ai.guiyunai.fun"
echo "2. 登录并测试图片上传功能"
echo "3. 检查上传的图片是否正常显示"
echo "4. 如有问题，查看容器日志: docker compose -f docker-compose.prod.yml logs postiz"
