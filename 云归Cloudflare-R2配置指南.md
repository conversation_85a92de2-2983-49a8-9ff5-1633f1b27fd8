# 🗂️ 云归Cloudflare R2配置指南

## 🚨 重要发现

经过深入分析，我发现了关键问题：

### ❌ Postiz不支持阿里云OSS
- Postiz代码中只支持 `local` 和 `cloudflare` 两种存储方式
- **没有原生的S3/AWS支持**
- 阿里云OSS的S3兼容API在Postiz中无法使用

### ✅ 推荐解决方案：Cloudflare R2
- **免费额度**：每月10GB存储 + 1000万次请求
- **S3兼容API**：完全兼容AWS S3 API
- **全球CDN**：自动提供全球加速
- **成本低廉**：超出免费额度后价格也很便宜

## 📋 Cloudflare R2配置步骤

### 第一步：创建Cloudflare账户和R2存储桶

1. **注册Cloudflare账户**
   - 访问：https://dash.cloudflare.com/sign-up
   - 完成邮箱验证

2. **启用R2服务**
   - 登录Cloudflare Dashboard
   - 在左侧菜单找到"R2 Object Storage"
   - 点击"Create bucket"

3. **创建存储桶**
   ```
   Bucket名称：guiyun-images-2025
   位置：自动（推荐）
   ```

### 第二步：获取API凭证

1. **创建API Token**
   - 在R2页面，点击"Manage R2 API tokens"
   - 点击"Create API token"
   - 权限设置：
     ```
     权限：Object Read & Write
     资源：包括所有账户资源
     ```

2. **记录重要信息**
   - Account ID：在R2页面右侧可以看到
   - Access Key ID：创建token后显示
   - Secret Access Key：创建token后显示（只显示一次）
   - Bucket URL：格式为 `https://ACCOUNT_ID.r2.cloudflarestorage.com`

### 第三步：配置域名（可选但推荐）

1. **设置自定义域名**
   - 在存储桶设置中，点击"Custom Domains"
   - 添加域名：如 `images.guiyunai.fun`
   - 按照提示添加DNS记录

2. **配置公共访问**
   - 在存储桶设置中，启用"Public access"
   - 这样图片可以直接通过URL访问

## 🔧 配置云归应用

### 更新环境变量

在 `docker-compose.prod.yml` 中，将以下占位符替换为您的实际值：

```yaml
# Cloudflare R2对象存储配置
STORAGE_PROVIDER: "cloudflare"

# Cloudflare R2配置 - 替换为您的实际值
CLOUDFLARE_ACCOUNT_ID: "您的Account ID"
CLOUDFLARE_ACCESS_KEY: "您的Access Key ID"
CLOUDFLARE_SECRET_ACCESS_KEY: "您的Secret Access Key"
CLOUDFLARE_BUCKETNAME: "guiyun-images-2025"
CLOUDFLARE_REGION: "auto"
CLOUDFLARE_BUCKET_URL: "https://您的Account_ID.r2.cloudflarestorage.com"
```

### 配置示例

假设您的信息如下：
```
Account ID: abc123def456
Access Key ID: f1234567890abcdef
Secret Access Key: xyz789secretkey
```

那么配置应该是：
```yaml
CLOUDFLARE_ACCOUNT_ID: "abc123def456"
CLOUDFLARE_ACCESS_KEY: "f1234567890abcdef"
CLOUDFLARE_SECRET_ACCESS_KEY: "xyz789secretkey"
CLOUDFLARE_BUCKETNAME: "guiyun-images-2025"
CLOUDFLARE_REGION: "auto"
CLOUDFLARE_BUCKET_URL: "https://abc123def456.r2.cloudflarestorage.com"
```

## 🚀 部署和测试

### 重启服务
```bash
cd /www/wwwroot/ai.guiyunai.fun
docker compose -f docker-compose.prod.yml down
docker compose -f docker-compose.prod.yml up -d
```

### 测试上传功能
1. 等待服务启动（约30秒）
2. 访问 https://ai.guiyunai.fun
3. 尝试上传图片测试
4. 检查图片是否正常显示

### 验证配置
```bash
# 检查环境变量
docker compose -f docker-compose.prod.yml exec postiz printenv | grep CLOUDFLARE

# 查看应用日志
docker compose -f docker-compose.prod.yml logs postiz --tail=20
```

## 💰 成本分析

### Cloudflare R2免费额度
- **存储**：10GB/月
- **Class A操作**：100万次/月（PUT, COPY, POST, LIST）
- **Class B操作**：1000万次/月（GET, HEAD）
- **出站流量**：免费（通过Cloudflare CDN）

### 超出免费额度的价格
- **存储**：$0.015/GB/月
- **Class A操作**：$4.50/百万次
- **Class B操作**：$0.36/百万次

对于大多数个人和小型企业，免费额度完全够用！

## 🔍 故障排除

### 如果图片仍然无法显示
1. **检查API凭证**：确保Account ID、Access Key等信息正确
2. **检查存储桶权限**：确保API token有读写权限
3. **检查网络**：确保服务器能访问Cloudflare
4. **查看日志**：检查应用日志中的错误信息

### 常见错误
- `Invalid storage type`：检查STORAGE_PROVIDER是否为"cloudflare"
- `Access denied`：检查API token权限设置
- `Bucket not found`：检查存储桶名称是否正确

## 📞 获取Cloudflare信息

请按照上述步骤获取以下信息，然后提供给我：

1. **Account ID**：12位字符串
2. **Access Key ID**：16位字符串  
3. **Secret Access Key**：40位字符串
4. **确认存储桶名称**：guiyun-images-2025

提供这些信息后，我将立即帮您完成配置！
