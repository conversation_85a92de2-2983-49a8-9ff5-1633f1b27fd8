#!/bin/bash

echo "🗂️ 配置阿里云OSS对象存储"
echo "========================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}使用方法：${NC}"
    echo "./configure-aliyun-oss.sh --bucket BUCKET_NAME --region REGION --access-key ACCESS_KEY --secret-key SECRET_KEY"
    echo ""
    echo -e "${BLUE}或者交互式配置：${NC}"
    echo "./configure-aliyun-oss.sh --interactive"
    echo ""
    echo -e "${GREEN}示例：${NC}"
    echo "./configure-aliyun-oss.sh --bucket postiz-images-2025 --region oss-cn-hangzhou --access-key LTAI5t... --secret-key abc123..."
    exit 1
fi

# 解析参数
INTERACTIVE=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --bucket)
            BUCKET_NAME="$2"
            shift 2
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        --access-key)
            ACCESS_KEY="$2"
            shift 2
            ;;
        --secret-key)
            SECRET_KEY="$2"
            shift 2
            ;;
        --interactive)
            INTERACTIVE=true
            shift
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 交互式配置
if [ "$INTERACTIVE" = true ]; then
    echo -e "${BLUE}交互式配置阿里云OSS${NC}"
    echo "请提供以下信息："
    echo ""
    
    read -p "请输入Bucket名称 (例如: postiz-images-2025): " BUCKET_NAME
    echo ""
    echo "常用地域："
    echo "华东1-杭州: oss-cn-hangzhou"
    echo "华北2-北京: oss-cn-beijing"
    echo "华南1-深圳: oss-cn-shenzhen"
    echo "华东2-上海: oss-cn-shanghai"
    echo ""
    read -p "请输入地域 (例如: oss-cn-hangzhou): " REGION
    read -p "请输入AccessKey ID: " ACCESS_KEY
    read -p "请输入AccessKey Secret: " SECRET_KEY
fi

# 验证参数
if [ -z "$BUCKET_NAME" ] || [ -z "$REGION" ] || [ -z "$ACCESS_KEY" ] || [ -z "$SECRET_KEY" ]; then
    echo -e "${RED}错误：缺少必要参数${NC}"
    echo "需要提供：--bucket, --region, --access-key, --secret-key"
    exit 1
fi

echo ""
echo -e "${GREEN}配置信息确认：${NC}"
echo "Bucket名称: $BUCKET_NAME"
echo "地域: $REGION"
echo "Access Key: ${ACCESS_KEY:0:8}..."
echo "Secret Key: ${SECRET_KEY:0:8}..."
echo ""

read -p "确认配置正确？(y/N): " confirm
if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "配置已取消"
    exit 1
fi

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo -e "${BLUE}第一步：备份当前配置${NC}"
echo "======================"

# 备份当前配置
BACKUP_FILE="docker-compose.prod.yml.backup.$(date +%Y%m%d_%H%M%S)"
cp docker-compose.prod.yml "$BACKUP_FILE"
echo "✅ 配置已备份到: $BACKUP_FILE"

echo ""
echo -e "${YELLOW}第二步：更新Docker Compose配置${NC}"
echo "=================================="

# 生成OSS配置
ENDPOINT="https://$REGION.aliyuncs.com"
PUBLIC_URL="https://$BUCKET_NAME.$REGION.aliyuncs.com"

echo "🔧 更新存储配置..."

# 替换存储配置为阿里云OSS
sed -i '/STORAGE_PROVIDER:/,/NEXT_PUBLIC_UPLOAD_URL:/c\
      # 阿里云OSS对象存储配置\
      STORAGE_PROVIDER: "s3"\
      \
      # 阿里云OSS配置（S3兼容）\
      AWS_S3_BUCKET: "'$BUCKET_NAME'"\
      AWS_S3_REGION: "'$REGION'"\
      AWS_S3_ENDPOINT: "'$ENDPOINT'"\
      AWS_ACCESS_KEY_ID: "'$ACCESS_KEY'"\
      AWS_SECRET_ACCESS_KEY: "'$SECRET_KEY'"\
      \
      # 公共访问URL\
      NEXT_PUBLIC_UPLOAD_URL: "'$PUBLIC_URL'"' docker-compose.prod.yml

echo "✅ Docker Compose配置已更新"

echo ""
echo -e "${GREEN}第三步：重启服务${NC}"
echo "=================="

echo "🔄 停止当前服务..."
docker compose -f docker-compose.prod.yml down

echo "🚀 启动服务（使用阿里云OSS）..."
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${BLUE}第四步：测试配置${NC}"
echo "=================="

# 检查服务状态
echo "📊 检查服务状态..."
docker ps --format "table {{.Names}}\t{{.Status}}" | grep postiz

# 检查环境变量
echo "📋 检查阿里云OSS配置..."
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "AWS_S3|STORAGE" | head -5

# 测试网站访问
echo "🌐 测试网站访问..."
MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "网站状态码: $MAIN_CODE"

# 测试OSS连接
echo "🔗 测试OSS连接..."
OSS_TEST=$(curl -s -o /dev/null -w "%{http_code}" "$PUBLIC_URL" 2>/dev/null || echo "000")
echo "OSS访问状态码: $OSS_TEST"

echo ""
echo -e "${GREEN}🎉 配置完成！${NC}"
echo "================"

echo "✅ 阿里云OSS已配置完成"
echo "✅ 存储桶: $BUCKET_NAME"
echo "✅ 地域: $REGION"
echo "✅ 公共URL: $PUBLIC_URL"

echo ""
echo -e "${YELLOW}下一步操作：${NC}"
echo "1. 访问 https://ai.guiyunai.fun"
echo "2. 测试图片上传功能"
echo "3. 验证图片显示正常"
echo "4. 享受稳定的云端图片存储！"

echo ""
echo -e "${BLUE}配置信息：${NC}"
echo "OSS Endpoint: $ENDPOINT"
echo "公共访问URL: $PUBLIC_URL"
echo "备份文件: $BACKUP_FILE"

if [ "$MAIN_CODE" = "200" ]; then
    echo ""
    echo -e "${GREEN}🎊 恭喜！服务运行正常，可以开始使用了！${NC}"
else
    echo ""
    echo -e "${YELLOW}⚠️ 服务可能需要更多时间启动，请稍等几分钟后再测试${NC}"
fi

echo ""
echo "📚 如需帮助："
echo "- 阿里云OSS控制台: https://oss.console.aliyun.com/"
echo "- Docker日志: docker compose -f docker-compose.prod.yml logs postiz"
echo ""
echo "💰 费用提醒："
echo "- 新用户通常有6个月免费额度"
echo "- 正常使用预计月费用: 5-10元"
echo "- 可在OSS控制台查看详细费用"
