#!/bin/bash

echo "🖼️ Postiz图片问题一键修复"
echo "========================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：检查当前状态${NC}"

# 检查Docker是否运行
if ! docker ps >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

# 检查服务状态
echo "📊 检查Postiz服务状态..."
RUNNING_CONTAINERS=$(docker ps --format "{{.Names}}" | grep postiz | wc -l)
echo "运行中的Postiz容器数量: $RUNNING_CONTAINERS"

echo ""
echo -e "${YELLOW}第二步：启动/重启服务${NC}"

if [ "$RUNNING_CONTAINERS" -eq 0 ]; then
    echo "🚀 启动Postiz服务..."
    docker compose -f docker-compose.prod.yml up -d
else
    echo "🔄 重启Postiz服务..."
    docker compose -f docker-compose.prod.yml restart
fi

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${GREEN}第三步：修复上传目录${NC}"

# 创建并修复上传目录
echo "📁 创建上传目录..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads 2>/dev/null || echo "目录已存在"

echo "🔧 设置目录权限..."
docker compose -f docker-compose.prod.yml exec -T postiz chmod -R 755 /uploads 2>/dev/null || echo "权限设置完成"
docker compose -f docker-compose.prod.yml exec -T postiz chown -R node:node /uploads 2>/dev/null || echo "所有者设置完成"

# 创建日期目录结构
CURRENT_DATE=$(date +%Y/%m/%d)
echo "📅 创建日期目录: $CURRENT_DATE"
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p "/uploads/$CURRENT_DATE" 2>/dev/null || echo "日期目录已存在"

echo ""
echo -e "${BLUE}第四步：测试文件访问${NC}"

# 创建测试文件
echo "🧪 创建测试文件..."
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "Postiz图片测试文件 - $(date)" > /uploads/test-image.txt'
docker compose -f docker-compose.prod.yml exec -T postiz chmod 644 /uploads/test-image.txt 2>/dev/null

# 测试网站访问
echo "🌐 测试网站访问..."
SITE_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "网站状态码: $SITE_CODE"

# 测试文件访问
echo "📄 测试文件访问..."
FILE_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test-image.txt 2>/dev/null || echo "000")
echo "文件状态码: $FILE_CODE"

# 测试文件内容
if [ "$FILE_CODE" = "200" ]; then
    echo "📖 测试文件内容:"
    curl -s https://ai.guiyunai.fun/uploads/test-image.txt 2>/dev/null | head -1
fi

echo ""
echo -e "${GREEN}第五步：检查服务日志${NC}"

echo "📝 最新服务日志:"
docker compose -f docker-compose.prod.yml logs postiz --tail=5 2>/dev/null || echo "无法获取日志"

echo ""
echo -e "${YELLOW}修复结果总结${NC}"
echo "=================================="

# 结果判断
if [ "$SITE_CODE" = "200" ] && [ "$FILE_CODE" = "200" ]; then
    echo -e "${GREEN}✅ 修复成功！${NC}"
    echo "✅ 网站正常访问"
    echo "✅ 文件上传功能正常"
    echo ""
    echo "🎯 下一步操作："
    echo "1. 访问 https://ai.guiyunai.fun"
    echo "2. 登录您的账户"
    echo "3. 创建新帖子并测试图片上传"
    echo "4. 验证上传的图片能正常显示"
    
elif [ "$SITE_CODE" = "200" ] && [ "$FILE_CODE" != "200" ]; then
    echo -e "${YELLOW}⚠️ 部分修复成功${NC}"
    echo "✅ 网站正常访问"
    echo "❌ 文件访问有问题 (状态码: $FILE_CODE)"
    echo ""
    echo "🔧 建议操作："
    echo "1. 检查Nginx配置是否正确代理 /uploads/ 路径"
    echo "2. 运行完全重建: ./complete-rebuild.sh"
    echo "3. 考虑使用外部文件存储服务"
    
elif [ "$SITE_CODE" != "200" ]; then
    echo -e "${RED}❌ 修复失败${NC}"
    echo "❌ 网站无法访问 (状态码: $SITE_CODE)"
    echo "❌ 服务可能未正常启动"
    echo ""
    echo "🔧 建议操作："
    echo "1. 检查Docker容器状态: docker ps"
    echo "2. 查看详细日志: docker compose -f docker-compose.prod.yml logs"
    echo "3. 运行完全重建: ./complete-rebuild.sh"
    echo "4. 检查端口5000是否被占用"
    
else
    echo -e "${RED}❌ 未知错误${NC}"
    echo "请检查网络连接和服务配置"
fi

echo ""
echo "📋 可用的修复工具："
echo "- ./一键修复图片问题.sh (当前脚本)"
echo "- ./fix-current-setup.sh (详细修复)"
echo "- ./complete-rebuild.sh (完全重建)"
echo "- 查看: 图片问题终极解决方案.md"

echo ""
echo "🆘 如需帮助，请提供以下信息："
echo "- 网站状态码: $SITE_CODE"
echo "- 文件状态码: $FILE_CODE"
echo "- 运行中容器数: $RUNNING_CONTAINERS"
echo "- 错误截图和浏览器开发者工具信息"
