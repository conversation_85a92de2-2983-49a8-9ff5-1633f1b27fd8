#!/bin/bash

echo "🖼️ 修复Postiz图片上传和显示问题"
echo "=================================="

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo "📊 检查当前服务状态..."
docker compose -f docker-compose.prod.yml ps

echo ""
echo "🔍 检查上传目录配置..."
echo "环境变量配置："
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "UPLOAD|STORAGE" || echo "未找到上传相关环境变量"

echo ""
echo "📁 检查容器内上传目录..."
echo "容器内 /uploads 目录："
docker compose -f docker-compose.prod.yml exec -T postiz ls -la /uploads/ 2>/dev/null || echo "无法访问 /uploads 目录"

echo ""
echo "💾 检查Docker卷..."
echo "Postiz相关卷："
docker volume ls | grep postiz || echo "未找到postiz相关卷"

echo ""
echo "🌐 测试上传端点..."
echo "测试 /uploads/ 路径："
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/)
echo "状态码: $HTTP_CODE"

echo ""
echo "🔧 尝试修复步骤..."

# 1. 确保上传目录存在
echo "1. 创建上传目录..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads
docker compose -f docker-compose.prod.yml exec -T postiz chmod 755 /uploads

# 2. 检查权限
echo "2. 设置目录权限..."
docker compose -f docker-compose.prod.yml exec -T postiz chown -R node:node /uploads 2>/dev/null || echo "权限设置可能需要root用户"

# 3. 重启服务
echo "3. 重启Postiz服务..."
docker compose -f docker-compose.prod.yml restart postiz

echo ""
echo "⏳ 等待服务重启..."
sleep 15

echo ""
echo "✅ 修复完成，重新测试..."
NEW_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/)
echo "修复后状态码: $NEW_CODE"

echo ""
echo "📝 检查最新日志..."
docker compose -f docker-compose.prod.yml logs postiz --tail=10

echo ""
echo "🔍 详细诊断..."

# 检查Postiz内部配置
echo "4. 检查Postiz内部配置..."
docker compose -f docker-compose.prod.yml exec -T postiz cat /app/package.json | grep -A5 -B5 "upload" 2>/dev/null || echo "无法读取package.json"

# 检查静态文件服务
echo "5. 测试静态文件服务..."
docker compose -f docker-compose.prod.yml exec -T postiz ls -la /app/public/ 2>/dev/null || echo "无法访问public目录"

# 创建测试文件
echo "6. 创建测试文件..."
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "test" > /uploads/test.txt' 2>/dev/null
TEST_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test.txt)
echo "测试文件访问状态码: $TEST_CODE"

# 检查环境变量
echo "7. 完整环境变量检查..."
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "URL|UPLOAD|STORAGE|PUBLIC" | head -10

echo ""
echo "🎯 下一步操作："
echo "1. 访问 https://ai.guiyunai.fun 测试图片上传"
echo "2. 如果测试文件访问失败($TEST_CODE)，需要配置Nginx静态文件服务"
echo "3. 检查防火墙和端口设置"
echo "4. 如果问题持续，可能需要配置外部文件存储"
