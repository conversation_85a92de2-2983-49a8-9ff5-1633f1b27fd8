#!/bin/bash

echo "🧪 测试云归图片上传功能"
echo "======================"

cd /www/wwwroot/ai.guiyunai.fun

echo "1. 检查服务状态..."
if docker ps | grep -q postiz; then
    echo "✅ Postiz服务运行中"
else
    echo "❌ Postiz服务未运行"
    exit 1
fi

echo ""
echo "2. 检查环境变量..."
echo "STORAGE_PROVIDER: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv STORAGE_PROVIDER 2>/dev/null)"
echo "FRONTEND_URL: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv FRONTEND_URL 2>/dev/null)"
echo "UPLOAD_DIRECTORY: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv UPLOAD_DIRECTORY 2>/dev/null)"

echo ""
echo "3. 创建测试文件..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads/test
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "Test upload file" > /uploads/test/test.txt'

echo ""
echo "4. 测试文件访问..."
echo "本地访问: $(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/uploads/test/test.txt 2>/dev/null)"
echo "外部访问: $(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test/test.txt 2>/dev/null)"

echo ""
echo "5. 测试网站主页..."
echo "网站状态: $(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null)"

echo ""
echo "✅ 测试完成！"
echo ""
echo "📋 下一步："
echo "1. 访问 https://ai.guiyunai.fun"
echo "2. 尝试上传图片"
echo "3. 检查图片是否正常显示"
