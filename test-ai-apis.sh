#!/bin/bash

echo "🤖 云归AI API密钥验证工具"
echo "========================"

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 从docker-compose.prod.yml提取API密钥
echo ""
echo -e "${BLUE}第一步：提取API密钥配置${NC}"
echo "=========================="

OPENAI_KEY=$(grep "OPENAI_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
GROQ_KEY=$(grep "GROQ_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
DEEPSEEK_KEY=$(grep "DEEPSEEK_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)

echo "OpenAI API Key: ${OPENAI_KEY:0:20}..."
echo "Groq API Key: ${GROQ_KEY:0:20}..."
echo "DeepSeek API Key: ${DEEPSEEK_KEY:0:20}..."

echo ""
echo -e "${BLUE}第二步：验证API密钥格式${NC}"
echo "=========================="

# 验证OpenAI API Key格式
if [[ $OPENAI_KEY =~ ^sk-proj-[A-Za-z0-9_-]{100,}$ ]]; then
    echo "✅ OpenAI API Key格式正确（项目密钥）"
elif [[ $OPENAI_KEY =~ ^sk-[A-Za-z0-9]{48}$ ]]; then
    echo "✅ OpenAI API Key格式正确（标准密钥）"
else
    echo "❌ OpenAI API Key格式错误"
fi

# 验证Groq API Key格式
if [[ $GROQ_KEY =~ ^gsk_[A-Za-z0-9]{50,}$ ]]; then
    echo "✅ Groq API Key格式正确"
else
    echo "❌ Groq API Key格式错误"
fi

# 验证DeepSeek API Key格式
if [[ $DEEPSEEK_KEY =~ ^sk-[A-Za-z0-9]{32}$ ]]; then
    echo "✅ DeepSeek API Key格式正确"
else
    echo "❌ DeepSeek API Key格式错误"
fi

echo ""
echo -e "${BLUE}第三步：测试API连通性${NC}"
echo "========================"

# 测试OpenAI API
echo "🧪 测试OpenAI API..."
OPENAI_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
  -H "Authorization: Bearer $OPENAI_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 5
  }' \
  https://api.openai.com/v1/chat/completions)

OPENAI_CODE=$(echo $OPENAI_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
OPENAI_BODY=$(echo $OPENAI_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

if [ "$OPENAI_CODE" = "200" ]; then
    echo "✅ OpenAI API连接成功"
elif [ "$OPENAI_CODE" = "401" ]; then
    echo "❌ OpenAI API密钥无效或过期"
elif [ "$OPENAI_CODE" = "429" ]; then
    echo "⚠️ OpenAI API额度不足或请求过频"
else
    echo "❌ OpenAI API连接失败，状态码: $OPENAI_CODE"
    echo "错误详情: $(echo $OPENAI_BODY | jq -r '.error.message' 2>/dev/null || echo $OPENAI_BODY)"
fi

# 测试Groq API
echo ""
echo "🧪 测试Groq API..."
GROQ_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
  -H "Authorization: Bearer $GROQ_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama3-8b-8192",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 5
  }' \
  https://api.groq.com/openai/v1/chat/completions)

GROQ_CODE=$(echo $GROQ_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
GROQ_BODY=$(echo $GROQ_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

if [ "$GROQ_CODE" = "200" ]; then
    echo "✅ Groq API连接成功"
elif [ "$GROQ_CODE" = "401" ]; then
    echo "❌ Groq API密钥无效或过期"
elif [ "$GROQ_CODE" = "429" ]; then
    echo "⚠️ Groq API额度不足或请求过频"
else
    echo "❌ Groq API连接失败，状态码: $GROQ_CODE"
    echo "错误详情: $(echo $GROQ_BODY | jq -r '.error.message' 2>/dev/null || echo $GROQ_BODY)"
fi

# 测试DeepSeek API
echo ""
echo "🧪 测试DeepSeek API..."
DEEPSEEK_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
  -H "Authorization: Bearer $DEEPSEEK_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 5
  }' \
  https://api.deepseek.com/chat/completions)

DEEPSEEK_CODE=$(echo $DEEPSEEK_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
DEEPSEEK_BODY=$(echo $DEEPSEEK_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

if [ "$DEEPSEEK_CODE" = "200" ]; then
    echo "✅ DeepSeek API连接成功"
elif [ "$DEEPSEEK_CODE" = "401" ]; then
    echo "❌ DeepSeek API密钥无效或过期"
elif [ "$DEEPSEEK_CODE" = "429" ]; then
    echo "⚠️ DeepSeek API额度不足或请求过频"
else
    echo "❌ DeepSeek API连接失败，状态码: $DEEPSEEK_CODE"
    echo "错误详情: $(echo $DEEPSEEK_BODY | jq -r '.error.message' 2>/dev/null || echo $DEEPSEEK_BODY)"
fi

echo ""
echo -e "${BLUE}第四步：检查应用内API配置${NC}"
echo "============================"

echo "🔍 检查容器内环境变量..."
echo "OpenAI Key: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv OPENAI_API_KEY 2>/dev/null | cut -c1-20)..."
echo "Groq Key: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv GROQ_API_KEY 2>/dev/null | cut -c1-20)..."
echo "DeepSeek Key: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv DEEPSEEK_API_KEY 2>/dev/null | cut -c1-20)..."

echo ""
echo -e "${BLUE}第五步：测试应用AI端点${NC}"
echo "=========================="

echo "🧪 测试Copilot Chat端点..."
COPILOT_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}]
  }' \
  https://ai.guiyunai.fun/api/copilot/chat)

COPILOT_CODE=$(echo $COPILOT_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')

if [ "$COPILOT_CODE" = "200" ]; then
    echo "✅ Copilot端点响应正常"
elif [ "$COPILOT_CODE" = "401" ]; then
    echo "❌ Copilot端点认证失败"
elif [ "$COPILOT_CODE" = "500" ]; then
    echo "❌ Copilot端点服务器错误"
else
    echo "❌ Copilot端点异常，状态码: $COPILOT_CODE"
fi

echo ""
echo -e "${GREEN}诊断总结${NC}"
echo "=========="

echo ""
echo "📊 API状态总结："
echo "OpenAI: $([ "$OPENAI_CODE" = "200" ] && echo "✅ 正常" || echo "❌ 异常($OPENAI_CODE)")"
echo "Groq: $([ "$GROQ_CODE" = "200" ] && echo "✅ 正常" || echo "❌ 异常($GROQ_CODE)")"
echo "DeepSeek: $([ "$DEEPSEEK_CODE" = "200" ] && echo "✅ 正常" || echo "❌ 异常($DEEPSEEK_CODE)")"
echo "Copilot: $([ "$COPILOT_CODE" = "200" ] && echo "✅ 正常" || echo "❌ 异常($COPILOT_CODE)")"

echo ""
echo "🔧 修复建议："
if [ "$OPENAI_CODE" != "200" ] && [ "$GROQ_CODE" != "200" ] && [ "$DEEPSEEK_CODE" != "200" ]; then
    echo "❌ 所有AI API都无法正常工作"
    echo "1. 检查网络连接"
    echo "2. 验证API密钥是否正确"
    echo "3. 检查API额度是否充足"
elif [ "$COPILOT_CODE" != "200" ]; then
    echo "❌ 应用AI端点异常"
    echo "1. 检查应用日志"
    echo "2. 重启Docker服务"
    echo "3. 验证环境变量配置"
else
    echo "✅ AI功能配置正常"
fi
