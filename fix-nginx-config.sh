#!/bin/bash

echo "🔧 修复Nginx配置以支持图片上传"
echo "=============================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：检查当前Nginx配置${NC}"
echo "=========================="

# 查找Nginx配置文件
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_CONF_DIR="/etc/nginx/conf.d"
NGINX_MAIN_CONF="/etc/nginx/nginx.conf"

echo "🔍 查找Nginx配置文件..."

if [ -d "$NGINX_SITES_DIR" ]; then
    echo "✅ 找到sites-available目录"
    ls -la $NGINX_SITES_DIR/ | grep -E "(guiyun|ai\.|default)"
elif [ -d "$NGINX_CONF_DIR" ]; then
    echo "✅ 找到conf.d目录"
    ls -la $NGINX_CONF_DIR/ | grep -E "(guiyun|ai\.|default)"
else
    echo "❌ 未找到标准Nginx配置目录"
fi

echo ""
echo -e "${BLUE}第二步：检查当前配置内容${NC}"
echo "=========================="

# 检查是否有现有的ai.guiyunai.fun配置
EXISTING_CONFIG=""
if [ -f "$NGINX_SITES_DIR/ai.guiyunai.fun" ]; then
    EXISTING_CONFIG="$NGINX_SITES_DIR/ai.guiyunai.fun"
elif [ -f "$NGINX_SITES_DIR/default" ]; then
    EXISTING_CONFIG="$NGINX_SITES_DIR/default"
elif [ -f "$NGINX_CONF_DIR/ai.guiyunai.fun.conf" ]; then
    EXISTING_CONFIG="$NGINX_CONF_DIR/ai.guiyunai.fun.conf"
elif [ -f "$NGINX_CONF_DIR/default.conf" ]; then
    EXISTING_CONFIG="$NGINX_CONF_DIR/default.conf"
fi

if [ -n "$EXISTING_CONFIG" ]; then
    echo "✅ 找到现有配置: $EXISTING_CONFIG"
    echo ""
    echo "🔍 检查uploads路径配置:"
    grep -n -A 5 -B 2 "location.*uploads" "$EXISTING_CONFIG" || echo "❌ 未找到uploads路径配置"
    echo ""
    echo "🔍 检查代理配置:"
    grep -n -A 3 "proxy_pass.*5000" "$EXISTING_CONFIG" || echo "❌ 未找到5000端口代理"
else
    echo "❌ 未找到现有Nginx配置文件"
fi

echo ""
echo -e "${BLUE}第三步：备份现有配置${NC}"
echo "======================"

if [ -n "$EXISTING_CONFIG" ]; then
    BACKUP_FILE="${EXISTING_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$EXISTING_CONFIG" "$BACKUP_FILE"
    echo "✅ 配置已备份到: $BACKUP_FILE"
else
    echo "ℹ️ 无现有配置需要备份"
fi

echo ""
echo -e "${BLUE}第四步：应用正确的Nginx配置${NC}"
echo "=============================="

# 确定配置文件位置
if [ -d "$NGINX_SITES_DIR" ]; then
    TARGET_CONFIG="$NGINX_SITES_DIR/ai.guiyunai.fun"
    ENABLE_CONFIG="/etc/nginx/sites-enabled/ai.guiyunai.fun"
else
    TARGET_CONFIG="$NGINX_CONF_DIR/ai.guiyunai.fun.conf"
    ENABLE_CONFIG=""
fi

echo "📝 创建新的Nginx配置..."

# 创建优化的Nginx配置
cat > "$TARGET_CONFIG" << 'EOF'
server {
    listen 80;
    listen 443 ssl http2;
    server_name ai.guiyunai.fun;

    # SSL配置（请根据实际情况调整证书路径）
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;

    # 安全头
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 上传文件大小限制
    client_max_body_size 100M;

    # 静态文件处理 - 代理到Postiz容器
    location /uploads/ {
        proxy_pass http://localhost:5000/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API路径
    location /api/ {
        proxy_pass http://localhost:5000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 主应用代理
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 健康检查
    location /health {
        proxy_pass http://localhost:5000/health;
        access_log off;
    }

    # 安全设置
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
EOF

echo "✅ Nginx配置文件已创建: $TARGET_CONFIG"

# 启用配置（如果使用sites-available/sites-enabled结构）
if [ -n "$ENABLE_CONFIG" ]; then
    if [ ! -L "$ENABLE_CONFIG" ]; then
        ln -s "$TARGET_CONFIG" "$ENABLE_CONFIG"
        echo "✅ 配置已启用: $ENABLE_CONFIG"
    else
        echo "ℹ️ 配置已经启用"
    fi
fi

echo ""
echo -e "${BLUE}第五步：测试和重载Nginx${NC}"
echo "========================="

echo "🧪 测试Nginx配置语法..."
if nginx -t; then
    echo "✅ Nginx配置语法正确"
    
    echo ""
    echo "🔄 重载Nginx配置..."
    if systemctl reload nginx; then
        echo "✅ Nginx配置已重载"
    else
        echo "❌ Nginx重载失败，尝试重启..."
        systemctl restart nginx
    fi
else
    echo "❌ Nginx配置语法错误，请检查配置"
    exit 1
fi

echo ""
echo -e "${BLUE}第六步：验证配置${NC}"
echo "=================="

echo "⏳ 等待Nginx重载完成..."
sleep 5

echo ""
echo "🧪 测试网站访问..."
MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "网站状态码: $MAIN_CODE"

if [ "$MAIN_CODE" = "200" ]; then
    echo "✅ 网站访问正常"
else
    echo "❌ 网站访问异常，状态码: $MAIN_CODE"
fi

echo ""
echo -e "${GREEN}配置完成！${NC}"
echo "=============="

echo ""
echo "📋 配置摘要:"
echo "- Nginx配置文件: $TARGET_CONFIG"
echo "- 备份文件: ${BACKUP_FILE:-无}"
echo "- 网站状态: $MAIN_CODE"

echo ""
echo "🧪 测试步骤:"
echo "1. 访问 https://ai.guiyunai.fun"
echo "2. 尝试上传图片"
echo "3. 检查图片是否正常显示"

echo ""
echo "🔍 如果仍有问题:"
echo "1. 检查SSL证书配置"
echo "2. 确认Docker容器运行在5000端口"
echo "3. 检查防火墙设置"
