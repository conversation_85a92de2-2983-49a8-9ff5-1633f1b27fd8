# 🆓 配置Cloudflare R2免费对象存储

## 🌟 为什么选择Cloudflare R2

**完全免费的优势：**
- ✅ **10GB存储空间** - 足够存储数千张图片
- ✅ **无限下载流量** - 图片访问完全免费
- ✅ **全球CDN加速** - 访问速度极快
- ✅ **99.9%可用性** - 比本地存储更稳定
- ✅ **S3兼容API** - Postiz原生支持

## 🚀 快速配置步骤

### 第一步：注册Cloudflare账户

1. 访问：https://dash.cloudflare.com/sign-up
2. 注册免费账户（只需邮箱）
3. 验证邮箱

### 第二步：启用R2服务

1. 登录Cloudflare控制台
2. 左侧菜单选择 **R2 Object Storage**
3. 点击 **Purchase R2 Plan**
4. 选择 **Free Plan** (0美元/月)
5. 添加付款方式（不会扣费，只是验证）

### 第三步：创建存储桶

1. 点击 **Create bucket**
2. 配置信息：
   ```
   Bucket名称：postiz-images-2025
   位置：Automatic (推荐)
   ```
3. 点击 **Create bucket**

### 第四步：获取API密钥

1. 右上角点击头像 → **My Profile**
2. 选择 **API Tokens** 标签
3. 点击 **Create Token**
4. 选择 **Custom token**
5. 配置权限：
   ```
   Token名称：Postiz R2 Access
   权限：
   - Account - Cloudflare R2:Edit
   - Zone Resources - Include - All zones
   ```
6. 点击 **Continue to summary** → **Create Token**
7. **保存生成的Token**

### 第五步：获取账户信息

1. 返回R2控制台
2. 右侧找到 **Account ID**，复制保存
3. 点击 **Manage R2 API tokens**
4. 创建R2专用Token：
   ```
   权限：Admin Read & Write
   ```
5. 保存 **Access Key ID** 和 **Secret Access Key**

## 🔧 Postiz配置

将以下配置添加到您的 `docker-compose.prod.yml`：

```yaml
environment:
  # 使用Cloudflare R2
  STORAGE_PROVIDER: "cloudflare"
  
  # Cloudflare R2配置
  CLOUDFLARE_ACCOUNT_ID: "your-account-id"
  CLOUDFLARE_ACCESS_KEY: "your-access-key-id"
  CLOUDFLARE_SECRET_ACCESS_KEY: "your-secret-access-key"
  CLOUDFLARE_BUCKETNAME: "postiz-images-2025"
  CLOUDFLARE_REGION: "auto"
  CLOUDFLARE_BUCKET_URL: "https://postiz-images-2025.your-account-id.r2.cloudflarestorage.com"
```

## 🛠️ 一键配置脚本

我为您准备了自动配置脚本，只需提供以下信息：

1. **Account ID**：在R2控制台右侧显示
2. **Access Key ID**：创建R2 Token时生成
3. **Secret Access Key**：创建R2 Token时生成
4. **Bucket名称**：您创建的存储桶名称

## 📊 免费额度详情

| 项目 | 免费额度 | 超出后价格 |
|------|----------|------------|
| 存储空间 | 10GB | $0.015/GB/月 |
| Class A操作 | 100万次/月 | $4.50/百万次 |
| Class B操作 | 1000万次/月 | $0.36/百万次 |
| 出站流量 | **无限制** | **永久免费** |

**对于您的使用场景：**
- 10GB ≈ 5000-10000张图片
- 无限流量 = 图片访问完全免费
- 100万次API调用 = 每天3万次操作

## 🎯 配置示例

假设您的信息如下：
```
Account ID: abc123def456
Access Key ID: f1234567890abcdef
Secret Access Key: xyz789secretkey
Bucket名称: postiz-images-2025
```

配置应该是：
```yaml
STORAGE_PROVIDER: "cloudflare"
CLOUDFLARE_ACCOUNT_ID: "abc123def456"
CLOUDFLARE_ACCESS_KEY: "f1234567890abcdef"
CLOUDFLARE_SECRET_ACCESS_KEY: "xyz789secretkey"
CLOUDFLARE_BUCKETNAME: "postiz-images-2025"
CLOUDFLARE_REGION: "auto"
CLOUDFLARE_BUCKET_URL: "https://postiz-images-2025.abc123def456.r2.cloudflarestorage.com"
```

## 🚀 自动配置命令

提供您的R2信息后，我会生成一键配置命令：

```bash
# 等待您提供信息后生成
./configure-cloudflare-r2.sh \
  --account-id "your-account-id" \
  --access-key "your-access-key" \
  --secret-key "your-secret-key" \
  --bucket "your-bucket-name"
```

## 🔄 迁移现有图片

配置完成后，现有的本地图片可以通过以下方式迁移：

1. **自动迁移**：重新上传图片
2. **手动迁移**：使用工具批量上传到R2
3. **混合模式**：新图片用R2，旧图片保持本地

## 📋 下一步

请按以下步骤操作：

1. **注册Cloudflare账户**
2. **创建R2存储桶**
3. **获取API密钥**
4. **提供配置信息给我**
5. **运行一键配置脚本**

完成后，您的图片将：
- ✅ 存储在全球CDN上
- ✅ 访问速度极快
- ✅ 完全免费（10GB内）
- ✅ 99.9%可用性保证

## 🆚 与其他方案对比

| 服务 | 存储 | 流量 | 速度 | 稳定性 | 推荐度 |
|------|------|------|------|--------|--------|
| Cloudflare R2 | 10GB | 无限 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 🏆 |
| Oracle Cloud | 20GB | 10TB | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 本地存储 | 无限 | 无限 | ⭐⭐⭐ | ⭐⭐ | ⭐⭐ |

现在请按照步骤注册Cloudflare R2，然后提供您的配置信息，我将为您生成完整的配置脚本！
