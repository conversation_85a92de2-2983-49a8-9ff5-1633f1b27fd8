#!/bin/bash

echo "🔧 Postiz 图片问题完整修复方案"
echo "=================================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo -e "${BLUE}第一步：诊断当前问题${NC}"
echo "=================================="

# 检查服务状态
echo "📊 检查服务状态..."
docker compose -f docker-compose.prod.yml ps

# 检查卷状态
echo "💾 检查Docker卷..."
docker volume ls | grep postiz

# 检查上传目录
echo "📁 检查上传目录..."
docker compose -f docker-compose.prod.yml exec -T postiz ls -la /uploads/ 2>/dev/null || echo "❌ 无法访问上传目录"

# 检查具体文件
echo "🔍 检查具体图片文件..."
docker compose -f docker-compose.prod.yml exec -T postiz find /uploads -name "*.jpeg" -o -name "*.jpg" -o -name "*.png" | head -5

# 测试直接文件访问
echo "🌐 测试文件访问..."
TEST_FILE=$(docker compose -f docker-compose.prod.yml exec -T postiz find /uploads -name "*.jpeg" -o -name "*.jpg" -o -name "*.png" | head -1 | tr -d '\r')
if [ ! -z "$TEST_FILE" ]; then
    RELATIVE_PATH=${TEST_FILE#/uploads/}
    echo "测试文件: $RELATIVE_PATH"
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://ai.guiyunai.fun/uploads/$RELATIVE_PATH")
    echo "直接访问状态码: $HTTP_CODE"
    
    # 测试Next.js优化访问
    ENCODED_URL=$(python3 -c "import urllib.parse; print(urllib.parse.quote('https://ai.guiyunai.fun/uploads/$RELATIVE_PATH', safe=''))")
    NEXTJS_URL="https://ai.guiyunai.fun/_next/image?url=$ENCODED_URL&w=128&q=75"
    NEXTJS_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$NEXTJS_URL")
    echo "Next.js优化访问状态码: $NEXTJS_CODE"
else
    echo "❌ 未找到上传的图片文件"
fi

echo ""
echo -e "${YELLOW}第二步：备份重要数据${NC}"
echo "=================================="

# 创建备份目录
BACKUP_DIR="/tmp/postiz-backup-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 备份配置文件..."
cp docker-compose.prod.yml "$BACKUP_DIR/"
cp .env "$BACKUP_DIR/" 2>/dev/null || echo "未找到.env文件"

echo "📦 备份上传文件..."
docker compose -f docker-compose.prod.yml exec -T postiz tar czf /tmp/uploads-backup.tar.gz /uploads/ 2>/dev/null || echo "❌ 备份上传文件失败"
docker cp postiz:/tmp/uploads-backup.tar.gz "$BACKUP_DIR/" 2>/dev/null || echo "❌ 复制备份文件失败"

echo "📦 备份数据库..."
docker compose -f docker-compose.prod.yml exec -T postiz-postgres pg_dump -U postiz-user postiz-db-local > "$BACKUP_DIR/database-backup.sql" 2>/dev/null || echo "❌ 备份数据库失败"

echo "✅ 备份完成，位置: $BACKUP_DIR"

echo ""
echo -e "${RED}第三步：选择修复方案${NC}"
echo "=================================="

echo "请选择修复方案："
echo "1) 尝试修复当前配置（推荐先尝试）"
echo "2) 完全重建（如果修复失败）"
echo "3) 仅诊断，不进行修复"

read -p "请输入选择 (1/2/3): " choice

case $choice in
    1)
        echo -e "${GREEN}执行配置修复...${NC}"
        ./fix-current-setup.sh
        ;;
    2)
        echo -e "${RED}执行完全重建...${NC}"
        ./complete-rebuild.sh "$BACKUP_DIR"
        ;;
    3)
        echo -e "${BLUE}仅诊断模式，不进行修复${NC}"
        ;;
    *)
        echo "无效选择，退出"
        exit 1
        ;;
esac

echo ""
echo "🎯 修复完成！"
echo "备份位置: $BACKUP_DIR"
echo "请测试图片上传和显示功能"
