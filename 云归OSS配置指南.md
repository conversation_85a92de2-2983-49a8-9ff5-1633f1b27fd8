# 🗂️ 云归阿里云OSS配置指南

## 🚨 解决"阻止公共访问"问题

您遇到的问题是阿里云OSS的安全机制。正确的解决方案是：

### ❌ 错误方式
- 设置Bucket为"公共读"
- 使用 `"Principal": "*"` 的公共访问策略

### ✅ 正确方式
- 创建专用RAM用户
- 使用私有Bucket + 授权策略
- 通过应用程序代理访问

## 📋 需要提供的信息

请按以下步骤操作并提供信息：

### 第一步：创建阿里云OSS Bucket

1. **登录阿里云控制台**
   - 访问：https://oss.console.aliyun.com/

2. **创建Bucket**
   ```
   Bucket名称：guiyun-images-2025
   地域：选择离您服务器最近的地域（推荐华东1-杭州）
   存储类型：标准存储
   读写权限：私有（重要！不要选择公共读）
   ```

3. **记录信息**
   - Bucket名称：`guiyun-images-2025`
   - 地域代码：如 `oss-cn-hangzhou`

### 第二步：创建RAM用户

1. **访问RAM控制台**
   - 访问：https://ram.console.aliyun.com/users

2. **创建用户**
   ```
   用户名：guiyun-oss-user
   访问方式：勾选"编程访问"
   ```

3. **添加权限**
   - 添加权限：`AliyunOSSFullAccess`

4. **记录密钥**
   - AccessKey ID：如 `LTAI5t...`
   - AccessKey Secret：如 `abc123...`

### 第三步：获取账户ID

1. **查看账户ID**
   - 在阿里云控制台右上角点击头像
   - 查看"账户ID"（12位数字）
   - 如：`123456789012`

## 🎯 需要提供给我的信息

请提供以下5个信息：

1. **Bucket名称**：`guiyun-images-2025`（建议使用这个）
2. **地域代码**：如 `oss-cn-hangzhou`
3. **AccessKey ID**：RAM用户的访问密钥ID
4. **AccessKey Secret**：RAM用户的访问密钥Secret
5. **阿里云账户ID**：12位数字的账户ID

## 🚀 配置命令

提供信息后，运行以下命令：

```bash
# 给脚本执行权限
chmod +x configure-guiyun-oss.sh

# 交互式配置
./configure-guiyun-oss.sh --interactive

# 或者直接配置（替换为您的实际信息）
./configure-guiyun-oss.sh \
  --bucket guiyun-images-2025 \
  --region oss-cn-hangzhou \
  --access-key YOUR_ACCESS_KEY \
  --secret-key YOUR_SECRET_KEY \
  --account-id YOUR_ACCOUNT_ID
```

## 📝 配置后的操作

### 第四步：应用Bucket Policy

1. **进入Bucket管理**
   - 在OSS控制台选择您的Bucket
   - 进入"权限管理" → "Bucket授权策略"

2. **添加策略**
   - 点击"新增授权"
   - 将 `oss-bucket-policy.json` 的内容复制粘贴
   - 确认创建

### 第五步：测试配置

```bash
# 检查服务状态
docker compose -f docker-compose.prod.yml ps

# 检查OSS配置
docker compose -f docker-compose.prod.yml exec postiz printenv | grep AWS_S3

# 测试网站访问
curl -I https://ai.guiyunai.fun
```

## 🔧 故障排除

### 如果仍然提示"阻止公共访问"

1. **确认Bucket设置**
   - Bucket读写权限必须是"私有"
   - 不要开启"公共读"

2. **检查Policy内容**
   - 确保使用了正确的账户ID
   - 确保RAM用户名正确

3. **验证RAM用户权限**
   - 确保RAM用户有OSS完整权限
   - 确保AccessKey有效

### 如果图片仍然无法显示

1. **检查应用配置**
   ```bash
   # 查看环境变量
   docker compose -f docker-compose.prod.yml exec postiz printenv | grep -E "UPLOAD|STORAGE|AWS"
   ```

2. **查看应用日志**
   ```bash
   # 查看最新日志
   docker compose -f docker-compose.prod.yml logs postiz --tail=50
   ```

3. **测试上传功能**
   - 尝试在应用中上传一张图片
   - 检查是否成功上传到OSS

## 💡 优势说明

使用这种配置方式的优势：

✅ **安全性高** - 不暴露公共访问权限
✅ **成本低廉** - 阿里云OSS按使用量计费
✅ **性能优秀** - CDN加速，全球访问快速
✅ **可靠性强** - 99.9%可用性保证
✅ **易于管理** - 通过RAM精确控制权限

请按照上述步骤操作，并提供所需的5个信息，我将帮您完成配置！
