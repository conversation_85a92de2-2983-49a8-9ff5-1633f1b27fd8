# 🚀 云归.中国 Postiz 社交媒体平台完整配置指南

## 📊 当前配置状态总结

### ✅ X (Twitter) 平台配置状态

**API密钥配置：**
- ✅ API Key: `*************************`
- ✅ API Key Secret: `A5MInTeREXLttl2zAeGDBYNCfVP8D6sone5JPTSd3YbGcbJZK2`
- ✅ 环境变量已正确配置到Docker容器
- ✅ 回调URL: `https://ai.guiyunai.fun/integrations/social/x`

**服务状态：**
- ⚠️ 需要重新启动服务（502错误）
- 📝 回调URL端点正常响应

## 🔧 立即修复502错误

### 第一步：重启Postiz服务
```bash
cd /www/wwwroot/ai.guiyunai.fun
docker compose -f docker-compose.prod.yml down
docker compose -f docker-compose.prod.yml up -d
```

### 第二步：检查服务状态
```bash
# 检查容器状态
docker compose -f docker-compose.prod.yml ps

# 检查日志
docker compose -f docker-compose.prod.yml logs postiz --tail=20

# 测试网站访问
curl -I https://ai.guiyunai.fun
```

## 📱 X (Twitter) 平台详细配置步骤

### 🔑 第一步：X Developer Portal配置（必须完成）

**1.1 访问X Developer Portal**
```
URL: https://developer.twitter.com/en/portal/dashboard
登录您的X开发者账户
```

**1.2 进入应用设置**
```
1. 点击您的应用：1950202572461277184LongWu13140
2. 点击 "Settings" 标签（不是 Keys and tokens）
```

**1.3 配置应用权限**
```
在 "App permissions" 部分：
☑️ 选择 "Read and Write"
```

**1.4 设置应用类型**
```
在 "Type of App" 部分：
☑️ 选择 "Web App, Automated App or Bot"
```

**1.5 配置回调URL（关键步骤）**
```
在 "App info" 部分找到 "Callback URLs"：
添加：https://ai.guiyunai.fun/integrations/social/x

注意：
- 必须使用 https://
- 确保域名完全正确
- 保存设置后等待1-2分钟生效
```

**1.6 填写必要的应用信息**
```
- App name: 云归中国Postiz
- App description: 社交媒体管理平台
- Website URL: https://ai.guiyunai.fun
- Terms of service: https://ai.guiyunai.fun/terms（可选）
- Privacy policy: https://ai.guiyunai.fun/privacy（可选）
```

### 🌐 第二步：在Postiz中连接X账户

**2.1 访问Postiz管理界面**
```
1. 打开：https://ai.guiyunai.fun
2. 使用管理员账户登录
```

**2.2 添加X频道**
```
1. 在主界面点击 "Add Channel" 或 "添加频道"
2. 选择 "X" 或 "Twitter"
3. 点击 "Connect" 或 "连接"
```

**2.3 完成OAuth授权**
```
1. 系统会跳转到X授权页面
2. 使用您的X账户登录
3. 点击 "Authorize app" 授权应用
4. 授权成功后自动跳转回Postiz
```

### 🧪 第三步：测试X平台功能

**3.1 创建测试帖子**
```
1. 在Postiz中点击 "Create Post" 或 "创建帖子"
2. 选择已连接的X账户
3. 输入测试内容："测试来自云归.中国Postiz的发布 #测试"
4. 选择 "Post Now" 立即发布或设置定时发布
```

**3.2 验证发布结果**
```
1. 检查您的X账户是否收到新帖子
2. 在Postiz中查看发布状态
3. 确认没有错误信息
```

## 📋 LinkedIn平台详细配置指南

### 🔍 LinkedIn配置前提条件

**重要说明：**
- LinkedIn个人账户：可以发布个人动态
- LinkedIn公司页面：需要创建公司页面才能发布公司内容
- 根据您的截图，您正在创建LinkedIn应用，这是正确的

### 🏢 第一步：LinkedIn应用配置

**1.1 完成当前的应用创建**
```
根据您的截图，请继续填写：
- 应用名称：云归中国Postiz（或 sionsphere）
- LinkedIn页面：https://ai.guiyunai.fun/
- 隐私政策URL：https://ai.guiyunai.fun/privacy
- 应用徽标：上传一个100x100像素的logo
```

**1.2 选择产品和权限**
```
在应用创建后，需要添加以下产品：
☑️ Sign In with LinkedIn using OpenID Connect
☑️ Share on LinkedIn
☑️ Marketing Developer Platform（如果可用）
```

**1.3 配置OAuth重定向URL**
```
在 "Auth" 标签中添加：
https://ai.guiyunai.fun/integrations/social/linkedin
```

### 🔑 第二步：获取LinkedIn API密钥

**2.1 获取Client ID和Secret**
```
在应用的 "Auth" 标签中：
- 复制 Client ID
- 复制 Client Secret
```

**2.2 配置Postiz**
```bash
cd /www/wwwroot/ai.guiyunai.fun
./configure-social-media.sh linkedin "您的Client_ID" "您的Client_Secret"
```

### 🏢 第三步：LinkedIn公司页面配置（可选）

**3.1 创建LinkedIn公司页面**
```
如果您需要发布公司内容：
1. 访问：https://www.linkedin.com/company/setup/new/
2. 填写公司信息：
   - 公司名称：云归.中国
   - 网站：https://ai.guiyunai.fun
   - 行业：软件开发/技术服务
   - 公司规模：选择适当规模
```

**3.2 验证公司页面**
```
1. 完成公司页面创建
2. 确保您是页面管理员
3. 在LinkedIn应用中关联公司页面
```

### 📝 LinkedIn配置检查清单

**应用配置：**
- [ ] LinkedIn应用已创建
- [ ] 产品权限已添加
- [ ] 回调URL已配置
- [ ] Client ID和Secret已获取

**Postiz配置：**
- [ ] API密钥已配置到环境变量
- [ ] 服务已重启
- [ ] LinkedIn频道连接成功

**测试验证：**
- [ ] 能够授权LinkedIn账户
- [ ] 能够发布测试内容
- [ ] 发布状态正常显示

## 🚨 故障排除指南

### 502错误解决方案
```bash
# 1. 检查容器状态
docker ps

# 2. 重启所有服务
cd /www/wwwroot/ai.guiyunai.fun
docker compose -f docker-compose.prod.yml restart

# 3. 查看错误日志
docker compose -f docker-compose.prod.yml logs postiz

# 4. 如果仍有问题，完全重建
docker compose -f docker-compose.prod.yml down
docker compose -f docker-compose.prod.yml up -d
```

### X连接失败解决方案
```
1. 确认X Developer Portal中回调URL正确
2. 检查API密钥是否匹配
3. 确认应用权限设置为"Read and Write"
4. 等待X设置生效（1-2分钟）
```

### LinkedIn连接失败解决方案
```
1. 确认LinkedIn应用状态为"Live"
2. 检查产品权限是否已批准
3. 确认回调URL格式正确
4. 验证Client ID和Secret无误
```

## 💼 LinkedIn详细配置说明

### 🔍 关于LinkedIn公司页面的重要说明

**您不一定需要创建公司页面！**

LinkedIn有两种发布方式：
1. **个人账户发布** - 使用您的个人LinkedIn账户发布动态
2. **公司页面发布** - 需要创建并管理公司页面

**推荐方案：先使用个人账户**
- 更简单快速
- 无需复杂的公司验证
- 适合个人品牌和小团队

### 📋 LinkedIn应用配置详细步骤

**第一步：完成应用基本信息**
```
根据您的截图，继续填写：
- 应用名称：sionsphere（已填写）
- LinkedIn页面：https://ai.guiyunai.fun/
- 隐私政策URL：https://ai.guiyunai.fun/privacy
- 应用徽标：上传logo（可以暂时使用默认图标）
```

**第二步：选择产品**
```
在应用创建后，添加以下产品：
☑️ Sign In with LinkedIn using OpenID Connect
☑️ Share on LinkedIn
注意：Marketing Developer Platform需要公司页面，个人使用可跳过
```

**第三步：配置权限范围**
```
在 "Products" 标签中确认以下权限：
- r_liteprofile（读取基本资料）
- r_emailaddress（读取邮箱）
- w_member_social（发布内容）
```

**第四步：设置重定向URL**
```
在 "Auth" 标签中添加：
https://ai.guiyunai.fun/integrations/social/linkedin

注意：
- 必须使用https://
- 确保域名完全正确
- 可以添加多个URL用于测试
```

### 🔑 获取LinkedIn API凭据

**在应用的Auth标签中：**
```
Client ID: [复制这个值]
Client Secret: [点击"Show"显示并复制]
```

**配置到Postiz：**
```bash
cd /www/wwwroot/ai.guiyunai.fun
./configure-social-media.sh linkedin "您的Client_ID" "您的Client_Secret"
```

### ⚠️ LinkedIn常见问题解决

**问题1：无法创建公司页面**
```
解决方案：使用个人账户即可
- LinkedIn个人账户可以发布动态
- 不需要公司页面也能使用Postiz
- 后续需要时再创建公司页面
```

**问题2：产品权限申请被拒**
```
解决方案：
- 确保应用信息完整
- 提供真实的隐私政策链接
- 详细描述应用用途
- 如被拒绝，可以重新申请
```

**问题3：API调用限制**
```
LinkedIn个人账户限制：
- 每天最多发布150个帖子
- 每小时最多25个API调用
- 对于个人使用完全足够
```

## 🎯 下一步行动计划

### 🚨 立即执行（现在）：
1. **修复502错误**
   ```bash
   cd /www/wwwroot/ai.guiyunai.fun
   ./check-status.sh
   ```

2. **完成X配置**
   - 在X Developer Portal设置回调URL: `https://ai.guiyunai.fun/integrations/social/x`
   - 确认应用权限为"Read and Write"

3. **完成LinkedIn应用**
   - 填写剩余的应用信息
   - 添加回调URL: `https://ai.guiyunai.fun/integrations/social/linkedin`
   - 获取Client ID和Secret

### 📅 今天完成：
1. **测试X连接** - 在Postiz中连接X账户
2. **配置LinkedIn API** - 使用获取的密钥配置Postiz
3. **测试发布功能** - 创建测试帖子验证两个平台

### 📊 本周目标：
1. **优化内容策略** - 设置定时发布计划
2. **添加更多平台** - Reddit、YouTube等
3. **团队协作设置** - 邀请团队成员

## 🛠️ 快速命令参考

```bash
# 检查服务状态
./check-status.sh

# 配置LinkedIn
./configure-social-media.sh linkedin "client_id" "client_secret"

# 重启服务
docker compose -f docker-compose.prod.yml restart

# 查看日志
docker compose -f docker-compose.prod.yml logs postiz --tail=20
```
