#!/bin/bash

echo "🔄 切换云归AI服务到Groq"
echo "===================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：验证Groq API可用性${NC}"
echo "========================="

GROQ_KEY=$(grep "GROQ_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)

if [ -z "$GROQ_KEY" ]; then
    echo "❌ 未找到Groq API密钥，请先配置"
    exit 1
fi

echo "🧪 测试Groq API..."
GROQ_TEST=$(curl -s -w "%{http_code}" \
  -H "Authorization: Bearer $GROQ_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama3-8b-8192",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 5
  }' \
  https://api.groq.com/openai/v1/chat/completions \
  -o /tmp/groq_test.json)

if [ "$GROQ_TEST" = "200" ]; then
    echo "✅ Groq API测试成功"
else
    echo "❌ Groq API测试失败，状态码: $GROQ_TEST"
    cat /tmp/groq_test.json 2>/dev/null
    exit 1
fi

echo ""
echo -e "${BLUE}第二步：备份原始文件${NC}"
echo "======================"

BACKUP_DIR="groq-switch-backup-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📁 创建备份目录: $BACKUP_DIR"

# 备份需要修改的文件
cp apps/backend/src/api/routes/copilot.controller.ts "$BACKUP_DIR/"
cp libraries/nestjs-libraries/src/openai/openai.service.ts "$BACKUP_DIR/" 2>/dev/null || true
cp libraries/nestjs-libraries/src/agent/agent.graph.service.ts "$BACKUP_DIR/" 2>/dev/null || true

echo "✅ 文件备份完成"

echo ""
echo -e "${BLUE}第三步：修改CopilotController${NC}"
echo "=========================="

echo "🔧 修改CopilotController以使用Groq..."

# 创建新的CopilotController
cat > apps/backend/src/api/routes/copilot.controller.ts << 'EOF'
import { Logger, Controller, Get, Post, Req, Res, Query } from '@nestjs/common';
import {
  CopilotRuntime,
  OpenAIAdapter,
  copilotRuntimeNestEndpoint,
} from '@copilotkit/runtime';
import { GetOrgFromRequest } from '@gitroom/nestjs-libraries/user/org.from.request';
import { Organization } from '@prisma/client';
import { SubscriptionService } from '@gitroom/nestjs-libraries/database/prisma/subscriptions/subscription.service';

@Controller('/copilot')
export class CopilotController {
  constructor(private _subscriptionService: SubscriptionService) {}
  @Post('/chat')
  chat(@Req() req: Request, @Res() res: Response) {
    if (
      process.env.GROQ_API_KEY === undefined ||
      process.env.GROQ_API_KEY === ''
    ) {
      Logger.warn('Groq API key not set, chat functionality will not work');
      return;
    }

    const copilotRuntimeHandler = copilotRuntimeNestEndpoint({
      endpoint: '/copilot/chat',
      runtime: new CopilotRuntime(),
      serviceAdapter: new OpenAIAdapter({
        apiKey: process.env.GROQ_API_KEY,
        baseURL: 'https://api.groq.com/openai/v1',
        model:
          // @ts-ignore
          req?.body?.variables?.data?.metadata?.requestType ===
          'TextareaCompletion'
            ? 'llama3-8b-8192'
            : 'llama3-70b-8192',
      }),
    });

    // @ts-ignore
    return copilotRuntimeHandler(req, res);
  }

  @Get('/credits')
  calculateCredits(
    @GetOrgFromRequest() organization: Organization,
    @Query('type') type: 'ai_images' | 'ai_videos',
  ) {
    return this._subscriptionService.checkCredits(organization, type || 'ai_images');
  }
}
EOF

echo "✅ CopilotController已更新为使用Groq"

echo ""
echo -e "${BLUE}第四步：创建Groq服务类${NC}"
echo "======================"

echo "🔧 创建GroqService..."

# 创建Groq服务目录
mkdir -p libraries/nestjs-libraries/src/groq

# 创建Groq服务
cat > libraries/nestjs-libraries/src/groq/groq.service.ts << 'EOF'
import { Injectable } from '@nestjs/common';

@Injectable()
export class GroqService {
  private apiKey: string;
  private baseURL: string = 'https://api.groq.com/openai/v1';

  constructor() {
    this.apiKey = process.env.GROQ_API_KEY || '';
  }

  async generateText(prompt: string, model: string = 'llama3-8b-8192'): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Groq API key not configured');
    }

    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: 'user', content: prompt }
        ],
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`Groq API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  async generatePosts(content: string): Promise<any[]> {
    const prompt = `Generate 5 different social media posts from the following content. Return as JSON array with format [{"post": "content"}]. Content: ${content}`;
    
    const response = await this.generateText(prompt, 'llama3-70b-8192');
    
    try {
      // 尝试解析JSON响应
      const start = response.indexOf('[');
      const end = response.lastIndexOf(']') + 1;
      if (start !== -1 && end !== -1) {
        return JSON.parse(response.slice(start, end));
      }
      return [];
    } catch (e) {
      console.error('Failed to parse Groq response:', e);
      return [];
    }
  }
}
EOF

echo "✅ GroqService已创建"

echo ""
echo -e "${BLUE}第五步：重启服务${NC}"
echo "=================="

echo "🔄 重启Docker服务..."
docker compose -f docker-compose.prod.yml down
sleep 5
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${BLUE}第六步：测试新配置${NC}"
echo "=================="

echo "🧪 测试网站访问..."
SITE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "网站状态码: $SITE_STATUS"

echo ""
echo "🧪 测试Copilot端点..."
COPILOT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "Hello"}]}' \
  https://ai.guiyunai.fun/api/copilot/chat)

echo "Copilot端点状态码: $COPILOT_STATUS"

if [ "$COPILOT_STATUS" = "200" ] || [ "$COPILOT_STATUS" = "401" ]; then
    echo "✅ Copilot端点响应正常"
else
    echo "❌ Copilot端点可能有问题"
fi

echo ""
echo -e "${GREEN}切换完成！${NC}"
echo "=============="

echo ""
echo "📋 修改摘要："
echo "✅ CopilotController已切换到Groq API"
echo "✅ 使用模型: llama3-8b-8192 (快速) / llama3-70b-8192 (高质量)"
echo "✅ API端点: https://api.groq.com/openai/v1"
echo "✅ 备份文件保存在: $BACKUP_DIR"

echo ""
echo "🧪 测试步骤："
echo "1. 访问 https://ai.guiyunai.fun/launches"
echo "2. 尝试使用AI Assistant功能"
echo "3. 检查AI回复是否正常工作"

echo ""
echo "📊 Groq优势："
echo "✅ 免费额度充足"
echo "✅ 响应速度极快"
echo "✅ 支持多种Llama模型"
echo "✅ 与OpenAI API兼容"

echo ""
echo "🔍 如果遇到问题："
echo "1. 检查Groq API密钥是否有效"
echo "2. 查看应用日志: docker compose -f docker-compose.prod.yml logs postiz"
echo "3. 恢复备份: cp $BACKUP_DIR/* apps/backend/src/api/routes/"

echo ""
echo "✅ AI服务已成功切换到Groq！"

# 清理临时文件
rm -f /tmp/groq_test.json
