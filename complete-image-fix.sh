#!/bin/bash

echo "🖼️ 完整修复Postiz图片上传和显示问题"
echo "======================================="

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo "第一步：检查当前问题..."

# 检查服务状态
echo "📊 检查服务状态..."
docker compose -f docker-compose.prod.yml ps

# 测试当前上传端点
echo "🌐 测试上传端点..."
UPLOAD_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/)
echo "上传端点状态码: $UPLOAD_CODE"

echo ""
echo "第二步：应用配置修复..."

# 重启服务以应用新的环境变量
echo "🔄 重启服务应用新配置..."
docker compose -f docker-compose.prod.yml down
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 20

echo ""
echo "第三步：验证修复结果..."

# 检查容器状态
echo "📊 检查容器状态..."
docker ps --format "table {{.Names}}\t{{.Status}}"

# 检查环境变量
echo "🔧 检查上传配置..."
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "UPLOAD|STORAGE" | head -5

# 创建测试目录和文件
echo "📁 创建测试文件..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "test image upload" > /uploads/test.txt'
docker compose -f docker-compose.prod.yml exec -T postiz chmod 755 /uploads
docker compose -f docker-compose.prod.yml exec -T postiz chmod 644 /uploads/test.txt

# 测试文件访问
echo "🧪 测试文件访问..."
TEST_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test.txt)
echo "测试文件状态码: $TEST_CODE"

if [ "$TEST_CODE" = "200" ]; then
    echo "✅ 静态文件服务正常"
else
    echo "❌ 静态文件服务异常，状态码: $TEST_CODE"
    echo "🔧 这可能需要Nginx配置调整"
fi

# 测试主站点
echo "🌐 测试主站点..."
MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "主站点状态码: $MAIN_CODE"

echo ""
echo "第四步：检查日志..."
echo "📝 最新日志（最后10行）："
docker compose -f docker-compose.prod.yml logs postiz --tail=10

echo ""
echo "🎯 修复结果总结："
echo "================================"
echo "主站点状态: $MAIN_CODE"
echo "上传端点状态: $UPLOAD_CODE"
echo "测试文件状态: $TEST_CODE"

if [ "$MAIN_CODE" = "200" ] && [ "$TEST_CODE" = "200" ]; then
    echo "✅ 修复成功！图片上传应该正常工作"
elif [ "$MAIN_CODE" = "200" ] && [ "$TEST_CODE" != "200" ]; then
    echo "⚠️ 主站点正常，但静态文件服务需要配置"
    echo "📋 建议操作："
    echo "1. 检查Nginx配置是否正确代理 /uploads/ 路径"
    echo "2. 确认Docker卷挂载正确"
    echo "3. 参考 nginx-postiz.conf 配置文件"
else
    echo "❌ 需要进一步诊断"
    echo "📋 建议操作："
    echo "1. 检查Docker容器日志"
    echo "2. 验证端口5000是否正常监听"
    echo "3. 检查防火墙设置"
fi

echo ""
echo "🔧 如果问题持续，请执行："
echo "1. ./fix-image-upload.sh - 运行详细诊断"
echo "2. 检查 nginx-postiz.conf 配置文件"
echo "3. 考虑使用外部文件存储（如阿里云OSS）"
