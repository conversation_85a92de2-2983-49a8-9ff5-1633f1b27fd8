#!/bin/bash

echo "🗂️ 配置云归阿里云OSS对象存储"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}使用方法：${NC}"
    echo "./configure-guiyun-oss.sh --bucket BUCKET_NAME --region REGION --access-key ACCESS_KEY --secret-key SECRET_KEY --account-id ACCOUNT_ID"
    echo ""
    echo -e "${BLUE}或者交互式配置：${NC}"
    echo "./configure-guiyun-oss.sh --interactive"
    echo ""
    echo -e "${GREEN}示例：${NC}"
    echo "./configure-guiyun-oss.sh --bucket guiyun-images-2025 --region oss-cn-hangzhou --access-key LTAI5t... --secret-key abc123... --account-id *********"
    echo ""
    echo -e "${BLUE}需要提供的信息：${NC}"
    echo "1. Bucket名称 (建议: guiyun-images-2025)"
    echo "2. 地域 (如: oss-cn-hangzhou)"
    echo "3. RAM用户的AccessKey ID"
    echo "4. RAM用户的AccessKey Secret"
    echo "5. 阿里云账户ID (12位数字)"
    exit 1
fi

# 解析参数
INTERACTIVE=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --bucket)
            BUCKET_NAME="$2"
            shift 2
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        --access-key)
            ACCESS_KEY="$2"
            shift 2
            ;;
        --secret-key)
            SECRET_KEY="$2"
            shift 2
            ;;
        --account-id)
            ACCOUNT_ID="$2"
            shift 2
            ;;
        --interactive)
            INTERACTIVE=true
            shift
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 交互式配置
if [ "$INTERACTIVE" = true ]; then
    echo -e "${BLUE}交互式配置云归阿里云OSS${NC}"
    echo "请提供以下信息："
    echo ""
    
    read -p "请输入Bucket名称 (建议: guiyun-images-2025): " BUCKET_NAME
    echo ""
    echo "常用地域："
    echo "华东1-杭州: oss-cn-hangzhou"
    echo "华北2-北京: oss-cn-beijing"
    echo "华南1-深圳: oss-cn-shenzhen"
    echo "华东2-上海: oss-cn-shanghai"
    echo ""
    read -p "请输入地域 (例如: oss-cn-hangzhou): " REGION
    read -p "请输入RAM用户的AccessKey ID: " ACCESS_KEY
    read -p "请输入RAM用户的AccessKey Secret: " SECRET_KEY
    read -p "请输入阿里云账户ID (12位数字): " ACCOUNT_ID
fi

# 验证必需参数
if [ -z "$BUCKET_NAME" ] || [ -z "$REGION" ] || [ -z "$ACCESS_KEY" ] || [ -z "$SECRET_KEY" ] || [ -z "$ACCOUNT_ID" ]; then
    echo -e "${RED}错误：缺少必需参数${NC}"
    echo "请提供所有必需的参数：bucket, region, access-key, secret-key, account-id"
    exit 1
fi

echo ""
echo -e "${GREEN}配置信息确认：${NC}"
echo "Bucket名称: $BUCKET_NAME"
echo "地域: $REGION"
echo "AccessKey ID: ${ACCESS_KEY:0:8}..."
echo "账户ID: $ACCOUNT_ID"
echo ""

read -p "确认配置信息正确？(y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "配置已取消"
    exit 1
fi

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo -e "${BLUE}第一步：备份当前配置${NC}"
echo "======================"

# 备份当前配置
BACKUP_FILE="docker-compose.prod.yml.backup.$(date +%Y%m%d_%H%M%S)"
cp docker-compose.prod.yml "$BACKUP_FILE"
echo "✅ 配置已备份到: $BACKUP_FILE"

echo ""
echo -e "${YELLOW}第二步：更新Bucket Policy${NC}"
echo "=============================="

# 更新 Bucket Policy 文件中的账户ID
sed -i "s/YOUR_ACCOUNT_ID/$ACCOUNT_ID/g" oss-bucket-policy.json
echo "✅ Bucket Policy已更新账户ID"

echo ""
echo -e "${YELLOW}第三步：更新Docker Compose配置${NC}"
echo "=================================="

# 生成OSS配置
ENDPOINT="https://$REGION.aliyuncs.com"
PUBLIC_URL="https://$BUCKET_NAME.$REGION.aliyuncs.com"

echo "🔧 更新存储配置..."

# 替换存储配置为阿里云OSS
sed -i '/STORAGE_PROVIDER:/,/NEXT_PUBLIC_UPLOAD_URL:/c\
      # 云归阿里云OSS对象存储配置\
      STORAGE_PROVIDER: "s3"\
      \
      # 阿里云OSS配置（S3兼容）\
      AWS_S3_BUCKET: "'$BUCKET_NAME'"\
      AWS_S3_REGION: "'$REGION'"\
      AWS_S3_ENDPOINT: "'$ENDPOINT'"\
      AWS_ACCESS_KEY_ID: "'$ACCESS_KEY'"\
      AWS_SECRET_ACCESS_KEY: "'$SECRET_KEY'"\
      \
      # 公共访问URL\
      NEXT_PUBLIC_UPLOAD_URL: "'$PUBLIC_URL'"' docker-compose.prod.yml

echo "✅ Docker Compose配置已更新"

echo ""
echo -e "${GREEN}第四步：重启服务${NC}"
echo "=================="

echo "🔄 停止当前服务..."
docker compose -f docker-compose.prod.yml down

echo "🚀 启动服务（使用云归阿里云OSS）..."
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${BLUE}第五步：测试配置${NC}"
echo "=================="

# 检查服务状态
echo "📊 检查服务状态..."
docker ps --format "table {{.Names}}\t{{.Status}}" | grep postiz

# 检查环境变量
echo "📋 检查阿里云OSS配置..."
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "AWS_S3|STORAGE" | head -5

# 测试网站访问
echo "🌐 测试网站访问..."
MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "网站状态码: $MAIN_CODE"

echo ""
echo -e "${GREEN}✅ 配置完成！${NC}"
echo "==================="
echo ""
echo -e "${BLUE}配置摘要：${NC}"
echo "存储提供商: 阿里云OSS"
echo "Bucket名称: $BUCKET_NAME"
echo "访问地址: $PUBLIC_URL"
echo "网站状态: $MAIN_CODE"
echo ""
echo -e "${YELLOW}下一步操作：${NC}"
echo "1. 在阿里云OSS控制台创建名为 '$BUCKET_NAME' 的Bucket"
echo "2. 设置Bucket读写权限为'私有'（不是公共读）"
echo "3. 在Bucket管理中应用 oss-bucket-policy.json 策略"
echo "4. 测试图片上传功能"
echo ""
echo -e "${BLUE}如果遇到问题，请检查：${NC}"
echo "- RAM用户是否有OSS完整权限"
echo "- Bucket Policy是否正确应用"
echo "- 网络连接是否正常"
