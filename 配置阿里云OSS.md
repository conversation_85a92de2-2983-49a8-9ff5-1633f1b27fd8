# 🗂️ 配置阿里云OSS解决图片问题

## 🚨 为什么需要外部存储

从您遇到的持续404错误来看，本地文件存储在Docker环境中可能存在以下问题：
1. **卷挂载问题** - Docker卷可能没有正确持久化
2. **Next.js路由冲突** - 复杂的重写规则导致文件访问问题
3. **权限问题** - 容器内文件权限设置复杂

**阿里云OSS是最稳定的解决方案**，因为：
- ✅ 文件直接存储在云端，避免本地存储问题
- ✅ 提供CDN加速，图片加载更快
- ✅ 高可用性和可靠性
- ✅ 成本低廉（每月几元钱）

## 🔧 配置步骤

### 第一步：创建阿里云OSS Bucket

1. **登录阿里云控制台**
   - 访问：https://oss.console.aliyun.com/
   - 登录您的阿里云账户

2. **创建Bucket**
   ```
   Bucket名称：postiz-images-[随机字符]
   地域：选择离您服务器最近的地域（如华东1-杭州）
   存储类型：标准存储
   读写权限：公共读
   ```

3. **配置跨域规则**
   - 进入Bucket管理 → 权限管理 → 跨域设置
   - 添加规则：
   ```
   来源：https://ai.guiyunai.fun
   允许Methods：GET, POST, PUT, DELETE, HEAD
   允许Headers：*
   暴露Headers：ETag, x-oss-request-id
   ```

### 第二步：获取访问密钥

1. **创建RAM用户**
   - 访问：https://ram.console.aliyun.com/users
   - 创建用户，勾选"编程访问"
   - 记录AccessKey ID和AccessKey Secret

2. **授权OSS权限**
   - 为RAM用户添加权限：AliyunOSSFullAccess

### 第三步：配置Postiz

将以下配置添加到您的 `docker-compose.prod.yml`：

```yaml
environment:
  # 改为使用S3兼容的OSS
  STORAGE_PROVIDER: "s3"
  
  # 阿里云OSS配置
  AWS_S3_BUCKET: "your-bucket-name"
  AWS_S3_REGION: "oss-cn-hangzhou"  # 根据您选择的地域调整
  AWS_S3_ENDPOINT: "https://oss-cn-hangzhou.aliyuncs.com"
  AWS_ACCESS_KEY_ID: "your-access-key-id"
  AWS_SECRET_ACCESS_KEY: "your-secret-access-key"
  
  # 公共访问URL
  NEXT_PUBLIC_UPLOAD_URL: "https://your-bucket-name.oss-cn-hangzhou.aliyuncs.com"
```

### 第四步：自动配置脚本

我为您创建了自动配置脚本，请提供以下信息：

1. **Bucket名称**：您创建的OSS Bucket名称
2. **地域**：选择的OSS地域（如：oss-cn-hangzhou）
3. **AccessKey ID**：RAM用户的AccessKey ID
4. **AccessKey Secret**：RAM用户的AccessKey Secret

## 🎯 配置示例

假设您的配置如下：
```
Bucket名称：postiz-images-2025
地域：华东1-杭州 (oss-cn-hangzhou)
AccessKey ID：LTAI5t...
AccessKey Secret：abc123...
```

那么配置应该是：
```yaml
STORAGE_PROVIDER: "s3"
AWS_S3_BUCKET: "postiz-images-2025"
AWS_S3_REGION: "oss-cn-hangzhou"
AWS_S3_ENDPOINT: "https://oss-cn-hangzhou.aliyuncs.com"
AWS_ACCESS_KEY_ID: "LTAI5t..."
AWS_SECRET_ACCESS_KEY: "abc123..."
NEXT_PUBLIC_UPLOAD_URL: "https://postiz-images-2025.oss-cn-hangzhou.aliyuncs.com"
```

## 🚀 快速配置命令

提供您的OSS信息后，我会为您生成一键配置命令：

```bash
# 示例命令（请等待我根据您的信息生成）
./configure-oss.sh \
  --bucket "your-bucket-name" \
  --region "oss-cn-hangzhou" \
  --access-key "your-access-key" \
  --secret-key "your-secret-key"
```

## 💰 成本估算

阿里云OSS成本非常低：
- **存储费用**：0.12元/GB/月
- **流量费用**：0.5元/GB（CDN回源）
- **请求费用**：0.01元/万次

对于个人博客/社交媒体应用：
- 存储1GB图片：约1.2元/月
- 月流量10GB：约5元/月
- **总计约6-10元/月**

## 🎯 优势对比

| 方案 | 稳定性 | 速度 | 成本 | 维护难度 |
|------|--------|------|------|----------|
| 本地存储 | ⚠️ 不稳定 | 🐌 一般 | 💰 免费 | 😰 复杂 |
| 阿里云OSS | ✅ 非常稳定 | 🚀 很快 | 💰 极低 | 😊 简单 |

## 📋 下一步

请提供您的阿里云OSS配置信息：
1. Bucket名称
2. 地域选择
3. AccessKey ID
4. AccessKey Secret

我将为您生成完整的配置脚本，一键解决所有图片问题！
