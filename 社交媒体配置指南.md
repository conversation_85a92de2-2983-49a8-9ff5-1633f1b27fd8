# 🚀 云归.中国 Postiz 社交媒体平台配置完整指南

## 📋 当前可用的社交媒体平台

根据您的Postiz界面，以下是所有可配置的社交媒体平台：

### ✅ 基础平台（相对容易配置）
1. **X (Twitter)** - 需要开发者账户
2. **LinkedIn** - 个人和公司页面
3. **Reddit** - 需要应用注册
4. **Discord** - 需要Bot令牌
5. **Slack** - 需要工作区权限
6. **Bluesky** - 相对简单
7. **Mastodon** - 支持自定义实例
8. **Telegram** - Bot配置
9. **Nostr** - 去中心化协议

### 🔶 中等难度平台
10. **Pinterest** - 需要商业账户
11. **Dribbble** - 需要专业账户
12. **Medium** - 需要API访问
13. **Dev.to** - 开发者社区
14. **Hashnode** - 技术博客平台
15. **WordPress** - 需要站点配置
16. **VK** - 俄罗斯社交网络
17. **Lemmy** - Reddit替代品
18. **Warpcast** - Farcaster协议

### 🔴 高难度平台（需要复杂配置）
19. **YouTube** - 需要Google OAuth和多个API
20. **TikTok** - 需要开发者账户和HTTPS
21. **Instagram (Facebook Business)** - 需要Meta Business验证
22. **Instagram (Standalone)** - 需要专业账户
23. **Facebook Page** - 需要页面管理权限
24. **Threads** - 需要Instagram连接

## 🛠️ 详细配置步骤

### 1. TikTok 配置（解决您当前的问题）

**前提条件：**
- 必须有HTTPS域名（您的 https://ai.guiyunai.fun 符合要求）
- 需要TikTok开发者账户
- 需要公开的隐私政策和服务条款页面

**步骤1：创建TikTok开发者应用**
```
1. 访问：https://developers.tiktok.com/apps
2. 点击"Create an app"
3. 填写应用信息：
   - App Name: 云归中国Postiz
   - Redirect URI: https://ai.guiyunai.fun/integrations/social/tiktok
```

**步骤2：配置应用设置**
```
1. 平台选择：勾选"Web"
2. 添加产品：
   - Login Kit
   - Content Posting API
3. 设置权限范围：
   - user.info.basic
   - video.create
   - video.publish
   - video.upload
   - user.info.profile
```

**步骤3：获取API密钥**
```
复制以下信息到docker-compose.prod.yml：
TIKTOK_CLIENT_ID: "您的16位客户端ID"
TIKTOK_CLIENT_SECRET: "您的32位客户端密钥"
```

### 2. YouTube 配置

**步骤1：创建Google Cloud项目**
```
1. 访问：https://console.cloud.google.com/
2. 创建新项目：云归中国Postiz
3. 启用以下API：
   - YouTube Data API v3
   - YouTube Analytics API
   - YouTube Reporting API
```

**步骤2：配置OAuth同意屏幕**
```
1. 选择"外部"用户类型
2. 填写应用信息
3. 添加测试用户（您的Google账户）
```

**步骤3：创建OAuth凭据**
```
1. 创建凭据 > OAuth客户端ID
2. 应用类型：Web应用
3. 授权重定向URI：https://ai.guiyunai.fun/integrations/social/youtube
```

**步骤4：配置环境变量**
```
YOUTUBE_CLIENT_ID: "您的Google客户端ID"
YOUTUBE_CLIENT_SECRET: "您的Google客户端密钥"
```

### 3. 其他平台快速配置

**X (Twitter):**
```
1. 访问：https://developer.twitter.com/
2. 创建应用，获取API Key和Secret
3. 回调URL：https://ai.guiyunai.fun/integrations/social/x

环境变量：
X_API_KEY: "您的API密钥"
X_API_SECRET: "您的API密钥密码"
```

**LinkedIn:**
```
1. 访问：https://www.linkedin.com/developers/apps
2. 创建应用，申请必要权限
3. 回调URL：https://ai.guiyunai.fun/integrations/social/linkedin

环境变量：
LINKEDIN_CLIENT_ID: "您的客户端ID"
LINKEDIN_CLIENT_SECRET: "您的客户端密钥"
```

**Instagram (Facebook Business):**
```
1. 访问：https://developers.facebook.com/
2. 创建Meta应用
3. 添加Instagram Basic Display产品
4. 回调URL：https://ai.guiyunai.fun/integrations/social/instagram

环境变量：
FACEBOOK_APP_ID: "您的Facebook应用ID"
FACEBOOK_APP_SECRET: "您的Facebook应用密钥"
```

## 🔧 立即修复步骤

### 第一步：重启服务以应用新配置
```bash
cd /www/wwwroot/ai.guiyunai.fun
docker compose -f docker-compose.prod.yml down
docker compose -f docker-compose.prod.yml up -d
```

### 第二步：配置至少一个简单平台进行测试
推荐先配置 **Bluesky** 或 **Mastodon**，因为它们相对简单：

**Bluesky配置：**
1. 注册Bluesky账户
2. 在Postiz中直接使用用户名和密码登录
3. 无需额外API配置

**Mastodon配置：**
1. 选择一个Mastodon实例（如 mastodon.social）
2. 在实例中创建应用
3. 获取客户端ID和密钥

### 第三步：逐步添加其他平台
建议按以下顺序配置：
1. Bluesky/Mastodon（测试基本功能）
2. X (Twitter)（常用平台）
3. LinkedIn（商业用途）
4. YouTube（视频内容）
5. TikTok（短视频）
6. Instagram（图片内容）

## ⚠️ 重要注意事项

1. **HTTPS要求**：所有OAuth回调都必须使用HTTPS
2. **域名一致性**：确保所有配置中的域名都是 `https://ai.guiyunai.fun`
3. **API限制**：某些平台有严格的审核流程
4. **测试环境**：建议先在测试环境配置，确认无误后再应用到生产环境

## 🎯 下一步行动计划

1. **立即执行**：重启Docker服务应用新配置
2. **今天完成**：配置1-2个简单平台进行测试
3. **本周完成**：配置主要使用的3-4个平台
4. **持续优化**：根据使用需求逐步添加其他平台
