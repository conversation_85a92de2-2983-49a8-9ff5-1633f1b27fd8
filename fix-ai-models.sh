#!/bin/bash

echo "🔧 修复云归AI模型配置问题"
echo "========================"

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：识别问题${NC}"
echo "=================="

echo "🔍 发现的问题："
echo "1. ❌ 代码中使用了错误的模型名称 'gpt-4.1'"
echo "2. ❌ 正确的OpenAI模型名称应该是 'gpt-4' 或 'gpt-4-turbo'"
echo "3. ❌ 这会导致OpenAI API返回400错误"

echo ""
echo -e "${BLUE}第二步：备份文件${NC}"
echo "=================="

# 备份相关文件
BACKUP_DIR="ai-fix-backup-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📁 创建备份目录: $BACKUP_DIR"

# 查找所有包含 gpt-4.1 的文件
echo "🔍 查找需要修复的文件..."
grep -r "gpt-4\.1" . --include="*.ts" --include="*.js" --include="*.tsx" --include="*.jsx" > "$BACKUP_DIR/files_to_fix.txt"

echo "📋 需要修复的文件："
cat "$BACKUP_DIR/files_to_fix.txt" | cut -d: -f1 | sort | uniq

# 备份这些文件
while IFS= read -r file; do
    if [ -f "$file" ]; then
        cp "$file" "$BACKUP_DIR/"
        echo "✅ 已备份: $file"
    fi
done < <(cat "$BACKUP_DIR/files_to_fix.txt" | cut -d: -f1 | sort | uniq)

echo ""
echo -e "${BLUE}第三步：修复模型名称${NC}"
echo "======================"

echo "🔧 将 'gpt-4.1' 替换为 'gpt-4-turbo'..."

# 修复所有文件中的模型名称
find . -name "*.ts" -o -name "*.js" -o -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/gpt-4\.1/gpt-4-turbo/g'

echo "✅ 模型名称修复完成"

echo ""
echo "🔍 验证修复结果..."
REMAINING_ISSUES=$(grep -r "gpt-4\.1" . --include="*.ts" --include="*.js" --include="*.tsx" --include="*.jsx" | wc -l)
if [ "$REMAINING_ISSUES" -eq 0 ]; then
    echo "✅ 所有 'gpt-4.1' 已成功替换"
else
    echo "⚠️ 仍有 $REMAINING_ISSUES 处需要手动检查"
    grep -r "gpt-4\.1" . --include="*.ts" --include="*.js" --include="*.tsx" --include="*.jsx"
fi

echo ""
echo -e "${BLUE}第四步：检查其他AI配置${NC}"
echo "========================"

echo "🔍 检查其他可能的问题..."

# 检查是否有其他错误的模型名称
echo "检查其他模型配置："
grep -r "gpt-image-1" . --include="*.ts" --include="*.js" 2>/dev/null && echo "⚠️ 发现 'gpt-image-1'，应该是 'dall-e-3'"

# 修复 gpt-image-1
find . -name "*.ts" -o -name "*.js" | xargs sed -i 's/gpt-image-1/dall-e-3/g'
echo "✅ 已修复图像模型名称"

echo ""
echo -e "${BLUE}第五步：重新构建和重启服务${NC}"
echo "=============================="

echo "🔄 重启Docker服务以应用修复..."
docker compose -f docker-compose.prod.yml down
sleep 5
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${BLUE}第六步：测试修复结果${NC}"
echo "======================"

echo "🧪 测试OpenAI API连接..."
OPENAI_KEY=$(grep "OPENAI_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)

if [ -n "$OPENAI_KEY" ]; then
    TEST_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
      -H "Authorization: Bearer $OPENAI_KEY" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "gpt-4-turbo",
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 5
      }' \
      https://api.openai.com/v1/chat/completions)
    
    STATUS_CODE=$(echo $TEST_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    
    if [ "$STATUS_CODE" = "200" ]; then
        echo "✅ OpenAI API测试成功（gpt-4-turbo）"
    else
        echo "❌ OpenAI API测试失败，状态码: $STATUS_CODE"
        echo "响应: $(echo $TEST_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')"
    fi
else
    echo "❌ 未找到OpenAI API密钥"
fi

echo ""
echo "🧪 测试Copilot端点..."
COPILOT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "Hello"}]}' \
  https://ai.guiyunai.fun/api/copilot/chat)

if [ "$COPILOT_STATUS" = "200" ]; then
    echo "✅ Copilot端点测试成功"
elif [ "$COPILOT_STATUS" = "401" ]; then
    echo "⚠️ Copilot端点需要认证（这是正常的）"
else
    echo "❌ Copilot端点测试失败，状态码: $COPILOT_STATUS"
fi

echo ""
echo "🧪 测试网站访问..."
SITE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "网站状态码: $SITE_STATUS"

echo ""
echo -e "${GREEN}修复完成！${NC}"
echo "=============="

echo ""
echo "📋 修复摘要："
echo "✅ 已将 'gpt-4.1' 替换为 'gpt-4-turbo'"
echo "✅ 已将 'gpt-image-1' 替换为 'dall-e-3'"
echo "✅ 已重启Docker服务"
echo "✅ 备份文件保存在: $BACKUP_DIR"

echo ""
echo "🧪 测试步骤："
echo "1. 访问 https://ai.guiyunai.fun/launches"
echo "2. 尝试使用AI Assistant功能"
echo "3. 检查是否还出现'An error occurred'错误"

echo ""
echo "🔍 如果问题仍然存在："
echo "1. 检查OpenAI API密钥是否有效"
echo "2. 检查API额度是否充足"
echo "3. 查看应用日志: docker compose -f docker-compose.prod.yml logs postiz"

echo ""
echo "📞 获取更多帮助："
echo "如果问题持续，请提供以下信息："
echo "- 具体的错误消息"
echo "- 浏览器开发者工具中的网络请求错误"
echo "- 应用日志中的相关错误信息"
