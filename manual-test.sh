#!/bin/bash

echo "手动测试云归图片上传功能"
echo "========================"

cd /www/wwwroot/ai.guiyunai.fun

# 1. 检查Docker服务
echo "1. 检查Docker服务..."
if docker ps | grep -q postiz; then
    echo "✅ Docker服务运行中"
    docker ps --format "table {{.Names}}\t{{.Status}}" | grep postiz
else
    echo "❌ Docker服务未运行，启动服务..."
    docker compose -f docker-compose.prod.yml up -d
    sleep 20
fi

# 2. 创建测试文件
echo ""
echo "2. 创建测试文件..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads/test
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "Test file created at $(date)" > /uploads/test/manual-test.txt'
docker compose -f docker-compose.prod.yml exec -T postiz chmod 644 /uploads/test/manual-test.txt

# 3. 验证文件创建
echo ""
echo "3. 验证文件创建..."
if docker compose -f docker-compose.prod.yml exec -T postiz test -f /uploads/test/manual-test.txt; then
    echo "✅ 测试文件创建成功"
    echo "文件内容："
    docker compose -f docker-compose.prod.yml exec -T postiz cat /uploads/test/manual-test.txt
else
    echo "❌ 测试文件创建失败"
    exit 1
fi

# 4. 测试本地访问
echo ""
echo "4. 测试本地访问..."
LOCAL_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" http://localhost:5000/uploads/test/manual-test.txt)
LOCAL_CODE=$(echo $LOCAL_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
LOCAL_BODY=$(echo $LOCAL_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

echo "本地访问状态码: $LOCAL_CODE"
if [ "$LOCAL_CODE" = "200" ]; then
    echo "✅ 本地访问成功"
    echo "响应内容: $LOCAL_BODY"
else
    echo "❌ 本地访问失败"
fi

# 5. 测试外部访问
echo ""
echo "5. 测试外部访问..."
EXTERNAL_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" https://ai.guiyunai.fun/uploads/test/manual-test.txt)
EXTERNAL_CODE=$(echo $EXTERNAL_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
EXTERNAL_BODY=$(echo $EXTERNAL_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

echo "外部访问状态码: $EXTERNAL_CODE"
if [ "$EXTERNAL_CODE" = "200" ]; then
    echo "✅ 外部访问成功"
    echo "响应内容: $EXTERNAL_BODY"
else
    echo "❌ 外部访问失败"
    echo "错误响应: $EXTERNAL_BODY"
fi

# 6. 测试网站主页
echo ""
echo "6. 测试网站主页..."
MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "网站主页状态码: $MAIN_CODE"

# 7. 检查环境变量
echo ""
echo "7. 检查关键环境变量..."
echo "STORAGE_PROVIDER: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv STORAGE_PROVIDER 2>/dev/null)"
echo "FRONTEND_URL: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv FRONTEND_URL 2>/dev/null)"
echo "UPLOAD_DIRECTORY: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv UPLOAD_DIRECTORY 2>/dev/null)"

# 8. 总结
echo ""
echo "========================"
echo "测试总结："
echo "Docker服务: $(docker ps | grep -q postiz && echo "✅" || echo "❌")"
echo "文件创建: ✅"
echo "本地访问: $([ "$LOCAL_CODE" = "200" ] && echo "✅" || echo "❌")"
echo "外部访问: $([ "$EXTERNAL_CODE" = "200" ] && echo "✅" || echo "❌")"
echo "网站主页: $([ "$MAIN_CODE" = "200" ] && echo "✅" || echo "❌")"

echo ""
echo "手动测试URL:"
echo "https://ai.guiyunai.fun/uploads/test/manual-test.txt"

if [ "$EXTERNAL_CODE" = "200" ]; then
    echo ""
    echo "🎉 图片上传功能应该正常工作！"
    echo "现在可以在应用中测试图片上传。"
else
    echo ""
    echo "⚠️ 需要进一步排查问题："
    if [ "$LOCAL_CODE" = "200" ] && [ "$EXTERNAL_CODE" != "200" ]; then
        echo "- 本地访问正常但外部访问失败，可能是Nginx配置问题"
        echo "- 检查Nginx是否正确配置了/uploads路径代理"
        echo "- 检查SSL证书和HTTPS配置"
    elif [ "$LOCAL_CODE" != "200" ]; then
        echo "- 本地访问失败，可能是应用配置问题"
        echo "- 检查FRONTEND_URL环境变量"
        echo "- 检查应用日志"
    fi
fi
