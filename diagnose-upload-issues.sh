#!/bin/bash

echo "🔍 云归图片上传问题全面诊断"
echo "============================"

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：检查Nginx配置${NC}"
echo "======================"

echo "📋 检查Nginx配置文件..."
if [ -f "/etc/nginx/sites-available/ai.guiyunai.fun" ]; then
    echo "✅ 找到Nginx配置文件"
    echo ""
    echo "🔍 检查/uploads路径配置："
    grep -A 10 -B 2 "location.*uploads" /etc/nginx/sites-available/ai.guiyunai.fun || echo "❌ 未找到uploads路径配置"
    echo ""
    echo "🔍 检查代理配置："
    grep -A 5 -B 2 "proxy_pass.*5000" /etc/nginx/sites-available/ai.guiyunai.fun || echo "❌ 未找到5000端口代理配置"
else
    echo "❌ 未找到Nginx配置文件"
    echo "检查其他可能的配置文件..."
    find /etc/nginx -name "*guiyun*" -o -name "*ai.*" 2>/dev/null || echo "未找到相关配置"
fi

echo ""
echo "📊 检查Nginx状态..."
systemctl is-active nginx && echo "✅ Nginx运行中" || echo "❌ Nginx未运行"

echo ""
echo "🧪 测试Nginx配置语法..."
nginx -t 2>&1 | head -5

echo ""
echo -e "${BLUE}第二步：检查Nginx缓存${NC}"
echo "======================"

echo "🗑️ 清理Nginx缓存..."
if [ -d "/var/cache/nginx" ]; then
    rm -rf /var/cache/nginx/*
    echo "✅ Nginx缓存已清理"
else
    echo "ℹ️ 未找到Nginx缓存目录"
fi

echo ""
echo "🔄 重新加载Nginx配置..."
nginx -s reload 2>&1 && echo "✅ Nginx配置已重新加载" || echo "❌ Nginx重新加载失败"

echo ""
echo -e "${BLUE}第三步：检查Docker卷配置${NC}"
echo "========================="

echo "📦 检查Docker卷列表..."
docker volume ls | grep postiz

echo ""
echo "🔍 检查postiz-uploads卷详情..."
docker volume inspect postiz-uploads 2>/dev/null || echo "❌ postiz-uploads卷不存在"

echo ""
echo "📂 检查卷挂载情况..."
docker compose -f docker-compose.prod.yml ps --format "table {{.Names}}\t{{.Status}}"

echo ""
echo "🗂️ 检查容器内挂载点..."
docker compose -f docker-compose.prod.yml exec -T postiz df -h | grep uploads || echo "❌ 未找到uploads挂载点"

echo ""
echo -e "${BLUE}第四步：检查文件权限${NC}"
echo "======================"

echo "👤 检查容器内uploads目录权限..."
docker compose -f docker-compose.prod.yml exec -T postiz ls -la / | grep uploads

echo ""
echo "📁 检查uploads目录内容..."
docker compose -f docker-compose.prod.yml exec -T postiz ls -la /uploads/ 2>/dev/null || echo "❌ 无法访问/uploads目录"

echo ""
echo "🔧 修复uploads目录权限..."
docker compose -f docker-compose.prod.yml exec -T postiz chmod -R 755 /uploads 2>/dev/null && echo "✅ 权限已修复" || echo "❌ 权限修复失败"

echo ""
echo "👥 检查容器用户..."
docker compose -f docker-compose.prod.yml exec -T postiz whoami 2>/dev/null || echo "无法获取用户信息"

echo ""
echo -e "${BLUE}第五步：检查防火墙和端口${NC}"
echo "=========================="

echo "🔥 检查防火墙状态..."
if command -v ufw >/dev/null 2>&1; then
    ufw status | head -10
elif command -v firewall-cmd >/dev/null 2>&1; then
    firewall-cmd --list-all | head -10
else
    echo "ℹ️ 未检测到常见防火墙工具"
fi

echo ""
echo "🌐 检查端口监听情况..."
echo "端口5000："
netstat -tlnp | grep :5000 || ss -tlnp | grep :5000 || echo "❌ 端口5000未监听"

echo ""
echo "端口80/443："
netstat -tlnp | grep -E ":(80|443)" | head -5 || ss -tlnp | grep -E ":(80|443)" | head -5

echo ""
echo "🧪 测试端口连通性..."
echo "本地5000端口："
timeout 5 bash -c "</dev/tcp/localhost/5000" 2>/dev/null && echo "✅ 本地5000端口可访问" || echo "❌ 本地5000端口不可访问"

echo ""
echo "本地80端口："
timeout 5 bash -c "</dev/tcp/localhost/80" 2>/dev/null && echo "✅ 本地80端口可访问" || echo "❌ 本地80端口不可访问"

echo ""
echo -e "${BLUE}第六步：创建和测试文件${NC}"
echo "========================="

echo "📝 创建测试文件..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads/test
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "Test file content - $(date)" > /uploads/test/test.txt'
docker compose -f docker-compose.prod.yml exec -T postiz chmod 644 /uploads/test/test.txt

echo ""
echo "🧪 测试文件访问..."
echo "直接容器访问："
docker compose -f docker-compose.prod.yml exec -T postiz cat /uploads/test/test.txt 2>/dev/null || echo "❌ 容器内无法读取文件"

echo ""
echo "本地HTTP访问："
LOCAL_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/uploads/test/test.txt 2>/dev/null)
echo "状态码: $LOCAL_CODE"
if [ "$LOCAL_CODE" = "200" ]; then
    echo "✅ 本地HTTP访问成功"
    curl -s http://localhost:5000/uploads/test/test.txt 2>/dev/null | head -1
else
    echo "❌ 本地HTTP访问失败"
fi

echo ""
echo "外部HTTPS访问："
EXTERNAL_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test/test.txt 2>/dev/null)
echo "状态码: $EXTERNAL_CODE"
if [ "$EXTERNAL_CODE" = "200" ]; then
    echo "✅ 外部HTTPS访问成功"
    curl -s https://ai.guiyunai.fun/uploads/test/test.txt 2>/dev/null | head -1
else
    echo "❌ 外部HTTPS访问失败"
fi

echo ""
echo -e "${BLUE}第七步：检查应用日志${NC}"
echo "======================"

echo "📋 检查Postiz应用日志..."
docker compose -f docker-compose.prod.yml logs postiz --tail=20 | grep -E "(error|Error|ERROR|upload|Upload)" || echo "未发现明显错误"

echo ""
echo "📋 检查Nginx访问日志..."
tail -10 /var/log/nginx/access.log 2>/dev/null | grep uploads || echo "未找到uploads相关访问记录"

echo ""
echo "📋 检查Nginx错误日志..."
tail -10 /var/log/nginx/error.log 2>/dev/null | tail -5 || echo "未找到Nginx错误日志"

echo ""
echo -e "${GREEN}诊断总结${NC}"
echo "=========="

echo ""
echo "🔍 关键检查项："
echo "1. Nginx配置: $([ -f "/etc/nginx/sites-available/ai.guiyunai.fun" ] && echo "✅" || echo "❌")"
echo "2. Docker卷: $(docker volume ls | grep -q postiz-uploads && echo "✅" || echo "❌")"
echo "3. 端口5000: $(netstat -tlnp 2>/dev/null | grep -q :5000 && echo "✅" || echo "❌")"
echo "4. 本地访问: $([ "$LOCAL_CODE" = "200" ] && echo "✅" || echo "❌")"
echo "5. 外部访问: $([ "$EXTERNAL_CODE" = "200" ] && echo "✅" || echo "❌")"

echo ""
echo "📋 下一步建议："
if [ "$EXTERNAL_CODE" != "200" ]; then
    echo "❌ 需要修复配置问题"
    echo "1. 检查Nginx配置中的uploads路径代理"
    echo "2. 确认Docker卷正确挂载"
    echo "3. 检查防火墙设置"
else
    echo "✅ 配置看起来正常，可以测试图片上传"
fi

echo ""
echo "🧪 手动测试："
echo "访问: https://ai.guiyunai.fun/uploads/test/test.txt"
