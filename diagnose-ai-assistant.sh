#!/bin/bash

echo "🤖 云归AI Assistant诊断工具"
echo "=========================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：检查AI API密钥配置${NC}"
echo "=========================="

# 检查环境变量
echo "📋 检查docker-compose.prod.yml中的API密钥..."
OPENAI_KEY=$(grep "OPENAI_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
GROQ_KEY=$(grep "GROQ_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
DEEPSEEK_KEY=$(grep "DEEPSEEK_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)

echo "OpenAI Key: ${OPENAI_KEY:0:15}... (长度: ${#OPENAI_KEY})"
echo "Groq Key: ${GROQ_KEY:0:15}... (长度: ${#GROQ_KEY})"
echo "DeepSeek Key: ${DEEPSEEK_KEY:0:15}... (长度: ${#DEEPSEEK_KEY})"

# 验证密钥格式
echo ""
echo "🔍 验证API密钥格式..."
if [[ $OPENAI_KEY =~ ^sk-proj- ]]; then
    echo "✅ OpenAI API Key格式正确（项目密钥）"
elif [[ $OPENAI_KEY =~ ^sk- ]]; then
    echo "✅ OpenAI API Key格式正确"
else
    echo "❌ OpenAI API Key格式错误"
fi

if [[ $GROQ_KEY =~ ^gsk_ ]]; then
    echo "✅ Groq API Key格式正确"
else
    echo "❌ Groq API Key格式错误"
fi

if [[ $DEEPSEEK_KEY =~ ^sk- ]]; then
    echo "✅ DeepSeek API Key格式正确"
else
    echo "❌ DeepSeek API Key格式错误"
fi

echo ""
echo -e "${BLUE}第二步：检查容器内环境变量${NC}"
echo "=========================="

echo "🔍 检查容器内API密钥..."
CONTAINER_OPENAI=$(docker compose -f docker-compose.prod.yml exec -T postiz printenv OPENAI_API_KEY 2>/dev/null)
CONTAINER_GROQ=$(docker compose -f docker-compose.prod.yml exec -T postiz printenv GROQ_API_KEY 2>/dev/null)
CONTAINER_DEEPSEEK=$(docker compose -f docker-compose.prod.yml exec -T postiz printenv DEEPSEEK_API_KEY 2>/dev/null)

if [ -n "$CONTAINER_OPENAI" ]; then
    echo "✅ 容器内OpenAI密钥已设置: ${CONTAINER_OPENAI:0:15}..."
else
    echo "❌ 容器内OpenAI密钥未设置"
fi

if [ -n "$CONTAINER_GROQ" ]; then
    echo "✅ 容器内Groq密钥已设置: ${CONTAINER_GROQ:0:15}..."
else
    echo "❌ 容器内Groq密钥未设置"
fi

if [ -n "$CONTAINER_DEEPSEEK" ]; then
    echo "✅ 容器内DeepSeek密钥已设置: ${CONTAINER_DEEPSEEK:0:15}..."
else
    echo "❌ 容器内DeepSeek密钥未设置"
fi

echo ""
echo -e "${BLUE}第三步：测试OpenAI API连通性${NC}"
echo "=========================="

echo "🧪 测试OpenAI API..."
if [ -n "$OPENAI_KEY" ]; then
    OPENAI_TEST=$(curl -s -w "%{http_code}" \
      -H "Authorization: Bearer $OPENAI_KEY" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "gpt-3.5-turbo",
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 5
      }' \
      https://api.openai.com/v1/chat/completions \
      -o /tmp/openai_test.json)
    
    if [ "$OPENAI_TEST" = "200" ]; then
        echo "✅ OpenAI API连接成功"
    elif [ "$OPENAI_TEST" = "401" ]; then
        echo "❌ OpenAI API密钥无效"
        cat /tmp/openai_test.json 2>/dev/null | head -3
    elif [ "$OPENAI_TEST" = "429" ]; then
        echo "⚠️ OpenAI API额度不足"
        cat /tmp/openai_test.json 2>/dev/null | head -3
    else
        echo "❌ OpenAI API连接失败，状态码: $OPENAI_TEST"
        cat /tmp/openai_test.json 2>/dev/null | head -3
    fi
else
    echo "❌ OpenAI API密钥未配置"
fi

echo ""
echo -e "${BLUE}第四步：测试应用AI端点${NC}"
echo "=========================="

echo "🧪 测试Copilot Chat端点..."
COPILOT_TEST=$(curl -s -w "%{http_code}" \
  -H "Content-Type: application/json" \
  -H "Cookie: session=test" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}]
  }' \
  https://ai.guiyunai.fun/api/copilot/chat \
  -o /tmp/copilot_test.json)

echo "Copilot端点状态码: $COPILOT_TEST"
if [ "$COPILOT_TEST" = "200" ]; then
    echo "✅ Copilot端点正常"
elif [ "$COPILOT_TEST" = "401" ]; then
    echo "❌ Copilot端点认证失败"
elif [ "$COPILOT_TEST" = "500" ]; then
    echo "❌ Copilot端点服务器错误"
    echo "错误详情:"
    cat /tmp/copilot_test.json 2>/dev/null | head -5
else
    echo "❌ Copilot端点异常"
    echo "响应内容:"
    cat /tmp/copilot_test.json 2>/dev/null | head -5
fi

echo ""
echo -e "${BLUE}第五步：检查应用日志${NC}"
echo "======================"

echo "📋 检查AI相关日志..."
docker compose -f docker-compose.prod.yml logs postiz --tail=30 2>/dev/null | grep -i -E "(openai|copilot|ai|chat|error)" | tail -10 || echo "未找到相关日志"

echo ""
echo -e "${BLUE}第六步：检查网络连接${NC}"
echo "======================"

echo "🌐 测试外部API连通性..."
echo "OpenAI API: $(curl -s -o /dev/null -w "%{http_code}" https://api.openai.com/v1/models --connect-timeout 5)"
echo "Groq API: $(curl -s -o /dev/null -w "%{http_code}" https://api.groq.com/openai/v1/models --connect-timeout 5)"
echo "DeepSeek API: $(curl -s -o /dev/null -w "%{http_code}" https://api.deepseek.com --connect-timeout 5)"

echo ""
echo -e "${GREEN}诊断总结${NC}"
echo "=========="

echo ""
echo "📊 问题分析："

# 分析可能的问题
ISSUES=0

if [ -z "$CONTAINER_OPENAI" ]; then
    echo "❌ 容器内OpenAI密钥未设置"
    ISSUES=$((ISSUES + 1))
fi

if [ "$OPENAI_TEST" != "200" ] && [ -n "$OPENAI_KEY" ]; then
    echo "❌ OpenAI API连接失败"
    ISSUES=$((ISSUES + 1))
fi

if [ "$COPILOT_TEST" != "200" ]; then
    echo "❌ Copilot端点异常"
    ISSUES=$((ISSUES + 1))
fi

if [ $ISSUES -eq 0 ]; then
    echo "✅ 未发现明显问题，AI功能应该正常"
else
    echo "⚠️ 发现 $ISSUES 个问题需要修复"
fi

echo ""
echo "🔧 修复建议："

if [ -z "$CONTAINER_OPENAI" ]; then
    echo "1. 重启Docker服务以加载环境变量"
    echo "   docker compose -f docker-compose.prod.yml restart"
fi

if [ "$OPENAI_TEST" = "401" ]; then
    echo "2. 更新OpenAI API密钥"
    echo "   检查密钥是否过期或无效"
fi

if [ "$OPENAI_TEST" = "429" ]; then
    echo "3. 检查OpenAI API额度"
    echo "   登录OpenAI控制台查看使用情况"
fi

if [ "$COPILOT_TEST" != "200" ]; then
    echo "4. 检查应用配置和日志"
    echo "   docker compose -f docker-compose.prod.yml logs postiz"
fi

echo ""
echo "🧪 手动测试步骤："
echo "1. 访问 https://ai.guiyunai.fun/launches"
echo "2. 尝试使用AI Assistant功能"
echo "3. 检查是否还出现'An error occurred'错误"

# 清理临时文件
rm -f /tmp/openai_test.json /tmp/copilot_test.json
