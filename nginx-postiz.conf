# Nginx配置文件用于Postiz静态文件服务
# 将此配置添加到您的Nginx站点配置中

server {
    listen 80;
    listen 443 ssl http2;
    server_name ai.guiyunai.fun;

    # SSL配置（如果使用HTTPS）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # 安全头
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 上传文件大小限制
    client_max_body_size 100M;

    # 静态文件处理 - 直接从Docker卷提供服务
    location /uploads/ {
        # 如果使用Docker卷，需要映射到主机路径
        # 或者代理到Postiz容器
        proxy_pass http://localhost:5000/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 错误处理
        proxy_intercept_errors on;
        error_page 404 = @fallback;
    }

    # 主应用代理
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 备用处理
    location @fallback {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查
    location /health {
        proxy_pass http://localhost:5000/health;
        access_log off;
    }

    # 安全设置
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# HTTP重定向到HTTPS（如果使用SSL）
server {
    listen 80;
    server_name ai.guiyunai.fun;
    return 301 https://$server_name$request_uri;
}
