#!/bin/bash

echo "🔧 修复云归本地存储图片显示问题"
echo "================================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo -e "${BLUE}第一步：分析当前问题${NC}"
echo "======================"

echo "检查当前配置..."
echo "STORAGE_PROVIDER: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv STORAGE_PROVIDER 2>/dev/null || echo '未设置')"
echo "UPLOAD_DIRECTORY: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv UPLOAD_DIRECTORY 2>/dev/null || echo '未设置')"
echo "FRONTEND_URL: $(docker compose -f docker-compose.prod.yml exec -T postiz printenv FRONTEND_URL 2>/dev/null || echo '未设置')"

echo ""
echo "检查Docker卷..."
docker volume ls | grep postiz-uploads || echo "未找到postiz-uploads卷"

echo ""
echo "检查容器内上传目录..."
docker compose -f docker-compose.prod.yml exec -T postiz ls -la /uploads/ 2>/dev/null || echo "无法访问/uploads目录"

echo ""
echo -e "${BLUE}第二步：备份当前配置${NC}"
echo "======================"

BACKUP_FILE="docker-compose.prod.yml.backup.$(date +%Y%m%d_%H%M%S)"
cp docker-compose.prod.yml "$BACKUP_FILE"
echo "✅ 配置已备份到: $BACKUP_FILE"

echo ""
echo -e "${YELLOW}第三步：修复环境变量配置${NC}"
echo "=============================="

echo "🔧 更新本地存储配置..."

# 检查并更新FRONTEND_URL
if ! grep -q "FRONTEND_URL:" docker-compose.prod.yml; then
    echo "添加FRONTEND_URL配置..."
    sed -i '/BACKEND_INTERNAL_URL:/a\      FRONTEND_URL: "https://ai.guiyunai.fun"' docker-compose.prod.yml
fi

# 确保FRONTEND_URL正确
sed -i 's|FRONTEND_URL:.*|FRONTEND_URL: "https://ai.guiyunai.fun"|' docker-compose.prod.yml

echo "✅ 环境变量配置已更新"

echo ""
echo -e "${YELLOW}第四步：确保Docker卷配置${NC}"
echo "=============================="

# 检查volumes配置
if grep -q "postiz-uploads:/uploads/" docker-compose.prod.yml; then
    echo "✅ Docker卷配置正确"
else
    echo "❌ Docker卷配置缺失，需要手动检查"
fi

echo ""
echo -e "${GREEN}第五步：重启服务${NC}"
echo "=================="

echo "🔄 停止当前服务..."
docker compose -f docker-compose.prod.yml down

echo "🚀 启动服务..."
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${BLUE}第六步：创建测试文件${NC}"
echo "======================"

echo "📁 创建测试目录和文件..."
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads/test
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "test image upload" > /uploads/test/test.txt'
docker compose -f docker-compose.prod.yml exec -T postiz chmod -R 755 /uploads

echo ""
echo -e "${BLUE}第七步：测试文件访问${NC}"
echo "======================"

echo "🧪 测试本地文件访问..."
LOCAL_TEST_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/uploads/test/test.txt 2>/dev/null || echo "000")
echo "本地访问状态码: $LOCAL_TEST_CODE"

echo "🌐 测试外部文件访问..."
EXTERNAL_TEST_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test/test.txt 2>/dev/null || echo "000")
echo "外部访问状态码: $EXTERNAL_TEST_CODE"

echo "🏠 测试网站主页..."
MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "网站状态码: $MAIN_CODE"

echo ""
echo -e "${BLUE}第八步：检查服务状态${NC}"
echo "======================"

echo "📊 检查容器状态..."
docker ps --format "table {{.Names}}\t{{.Status}}" | grep postiz || echo "未找到postiz容器"

echo "📋 检查环境变量..."
echo "当前配置："
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "STORAGE|UPLOAD|FRONTEND" | head -10

echo "📝 检查最新日志..."
docker compose -f docker-compose.prod.yml logs postiz --tail=10

echo ""
echo -e "${GREEN}第九步：诊断结果${NC}"
echo "=================="

if [ "$MAIN_CODE" = "200" ]; then
    echo "✅ 网站访问正常"
else
    echo "❌ 网站访问异常，状态码: $MAIN_CODE"
fi

if [ "$LOCAL_TEST_CODE" = "200" ]; then
    echo "✅ 本地文件访问正常"
elif [ "$EXTERNAL_TEST_CODE" = "200" ]; then
    echo "✅ 外部文件访问正常"
else
    echo "❌ 文件访问异常"
    echo "本地状态码: $LOCAL_TEST_CODE"
    echo "外部状态码: $EXTERNAL_TEST_CODE"
fi

echo ""
echo -e "${YELLOW}修复建议${NC}"
echo "=========="

if [ "$EXTERNAL_TEST_CODE" != "200" ]; then
    echo "🔧 如果文件仍然无法访问，请检查："
    echo "1. Nginx配置是否正确代理/uploads路径"
    echo "2. 防火墙是否阻止了文件访问"
    echo "3. Docker卷是否正确挂载"
    echo ""
    echo "📋 手动测试步骤："
    echo "1. 访问 https://ai.guiyunai.fun/uploads/test/test.txt"
    echo "2. 在应用中尝试上传图片"
    echo "3. 检查上传的图片是否能正常显示"
fi

echo ""
echo -e "${GREEN}✅ 修复完成！${NC}"
echo "=================="
echo ""
echo "📝 配置摘要："
echo "存储提供商: local"
echo "上传目录: /uploads"
echo "前端URL: https://ai.guiyunai.fun"
echo "网站状态: $MAIN_CODE"
echo "文件访问: $EXTERNAL_TEST_CODE"
