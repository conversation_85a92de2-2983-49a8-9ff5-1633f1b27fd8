user                            www;
worker_processes                auto; # it will be determinate automatically by the number of core

events {
    worker_connections          1024;
}

http {
    include                     /etc/nginx/mime.types;
    default_type                application/octet-stream;
    sendfile                    on;
    access_log                  /var/log/nginx/access.log;
    client_max_body_size 2G;
    server {
        listen 5000;
        server_name _;
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

        location /api/ {
            proxy_pass http://localhost:3000/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Reload $http_reload;
            proxy_set_header Onboarding $http_onboarding;
            proxy_set_header Activate $http_activate;
            proxy_set_header Auth $http_auth;
            proxy_set_header Showorg $http_showorg;
            proxy_set_header Impersonate $http_impersonate;
            proxy_set_header Accept-Language $http_accept_language;
        }

        location / {
            proxy_pass http://localhost:4200/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Reload $http_reload;
            proxy_set_header Onboarding $http_onboarding;
            proxy_set_header Activate $http_activate;
            proxy_set_header Auth $http_auth;
            proxy_set_header Showorg $http_showorg;
            proxy_set_header Impersonate $http_impersonate;
            proxy_set_header Accept-Language $http_accept_language;
            proxy_set_header i18next $http_i18next;
        }
    }
}