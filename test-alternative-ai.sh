#!/bin/bash

echo "🤖 测试云归备选AI模型"
echo "===================="

cd /www/wwwroot/ai.guiyunai.fun

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 从配置文件提取API密钥
GROQ_KEY=$(grep "GROQ_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)
DEEPSEEK_KEY=$(grep "DEEPSEEK_API_KEY:" docker-compose.prod.yml | cut -d'"' -f2)

echo ""
echo -e "${BLUE}第一步：检查备选AI服务配置${NC}"
echo "=========================="

echo "📋 当前配置的AI服务："
echo "Groq API Key: ${GROQ_KEY:0:20}..."
echo "DeepSeek API Key: ${DEEPSEEK_KEY:0:20}..."

echo ""
echo -e "${BLUE}第二步：测试Groq API${NC}"
echo "==================="

echo "🧪 测试Groq API连接和模型..."

if [ -n "$GROQ_KEY" ]; then
    # 测试Groq API - 获取可用模型
    echo "📋 获取Groq可用模型..."
    GROQ_MODELS=$(curl -s \
      -H "Authorization: Bearer $GROQ_KEY" \
      https://api.groq.com/openai/v1/models)
    
    if echo "$GROQ_MODELS" | grep -q "llama"; then
        echo "✅ Groq API连接成功"
        echo "可用模型："
        echo "$GROQ_MODELS" | jq -r '.data[].id' 2>/dev/null | head -5 || echo "$GROQ_MODELS" | grep -o '"id":"[^"]*"' | head -5
    else
        echo "❌ Groq API连接失败"
        echo "错误响应: $GROQ_MODELS"
    fi
    
    echo ""
    echo "🧪 测试Groq聊天功能..."
    GROQ_CHAT=$(curl -s -w "HTTPSTATUS:%{http_code}" \
      -H "Authorization: Bearer $GROQ_KEY" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "llama3-8b-8192",
        "messages": [
          {"role": "user", "content": "你好，请用中文回复一句简短的问候"}
        ],
        "max_tokens": 50,
        "temperature": 0.7
      }' \
      https://api.groq.com/openai/v1/chat/completions)
    
    GROQ_STATUS=$(echo $GROQ_CHAT | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    GROQ_RESPONSE=$(echo $GROQ_CHAT | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$GROQ_STATUS" = "200" ]; then
        echo "✅ Groq聊天测试成功"
        echo "AI回复: $(echo $GROQ_RESPONSE | jq -r '.choices[0].message.content' 2>/dev/null || echo "解析失败")"
    else
        echo "❌ Groq聊天测试失败，状态码: $GROQ_STATUS"
        echo "错误详情: $GROQ_RESPONSE"
    fi
else
    echo "❌ Groq API密钥未配置"
fi

echo ""
echo -e "${BLUE}第三步：测试DeepSeek API${NC}"
echo "======================"

echo "🧪 测试DeepSeek API连接..."

if [ -n "$DEEPSEEK_KEY" ]; then
    # 测试DeepSeek API
    DEEPSEEK_CHAT=$(curl -s -w "HTTPSTATUS:%{http_code}" \
      -H "Authorization: Bearer $DEEPSEEK_KEY" \
      -H "Content-Type: application/json" \
      -d '{
        "model": "deepseek-chat",
        "messages": [
          {"role": "user", "content": "你好，请用中文回复一句简短的问候"}
        ],
        "max_tokens": 50,
        "temperature": 0.7
      }' \
      https://api.deepseek.com/chat/completions)
    
    DEEPSEEK_STATUS=$(echo $DEEPSEEK_CHAT | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    DEEPSEEK_RESPONSE=$(echo $DEEPSEEK_CHAT | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$DEEPSEEK_STATUS" = "200" ]; then
        echo "✅ DeepSeek聊天测试成功"
        echo "AI回复: $(echo $DEEPSEEK_RESPONSE | jq -r '.choices[0].message.content' 2>/dev/null || echo "解析失败")"
    else
        echo "❌ DeepSeek聊天测试失败，状态码: $DEEPSEEK_STATUS"
        echo "错误详情: $DEEPSEEK_RESPONSE"
    fi
else
    echo "❌ DeepSeek API密钥未配置"
fi

echo ""
echo -e "${BLUE}第四步：测试其他免费AI服务${NC}"
echo "=========================="

echo "🧪 测试Ollama本地模型（如果可用）..."
OLLAMA_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:11434/api/tags --connect-timeout 3)
if [ "$OLLAMA_STATUS" = "200" ]; then
    echo "✅ 检测到本地Ollama服务"
    OLLAMA_MODELS=$(curl -s http://localhost:11434/api/tags | jq -r '.models[].name' 2>/dev/null | head -3)
    echo "可用模型: $OLLAMA_MODELS"
else
    echo "ℹ️ 未检测到本地Ollama服务"
fi

echo ""
echo "🧪 测试HuggingFace Inference API..."
HF_TEST=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Content-Type: application/json" \
  -d '{"inputs": "Hello"}' \
  https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium \
  --connect-timeout 5)

if [ "$HF_TEST" = "200" ]; then
    echo "✅ HuggingFace API可访问"
else
    echo "ℹ️ HuggingFace API状态: $HF_TEST"
fi

echo ""
echo -e "${BLUE}第五步：推荐最佳备选方案${NC}"
echo "=========================="

echo "📊 AI服务测试结果总结："
echo "Groq: $([ "$GROQ_STATUS" = "200" ] && echo "✅ 可用" || echo "❌ 不可用")"
echo "DeepSeek: $([ "$DEEPSEEK_STATUS" = "200" ] && echo "✅ 可用" || echo "❌ 不可用")"
echo "Ollama: $([ "$OLLAMA_STATUS" = "200" ] && echo "✅ 可用" || echo "ℹ️ 未安装")"

echo ""
echo "🎯 推荐方案："

if [ "$GROQ_STATUS" = "200" ]; then
    echo "✅ 推荐使用Groq作为主要AI服务"
    echo "   - 免费额度充足"
    echo "   - 响应速度快"
    echo "   - 支持多种Llama模型"
elif [ "$DEEPSEEK_STATUS" = "200" ]; then
    echo "✅ 推荐使用DeepSeek作为主要AI服务"
    echo "   - 中文支持优秀"
    echo "   - 成本较低"
    echo "   - 性能良好"
else
    echo "⚠️ 建议配置以下免费AI服务："
    echo "1. Groq (推荐) - 免费额度大，速度快"
    echo "2. DeepSeek - 中文友好，价格便宜"
    echo "3. Ollama - 完全免费的本地部署"
fi

echo ""
echo -e "${BLUE}第六步：配置修改建议${NC}"
echo "======================"

if [ "$GROQ_STATUS" = "200" ]; then
    echo "🔧 建议修改应用配置使用Groq："
    echo "1. 修改CopilotController使用Groq API"
    echo "2. 更新模型名称为: llama3-8b-8192"
    echo "3. 调整API端点为: https://api.groq.com/openai/v1/"
elif [ "$DEEPSEEK_STATUS" = "200" ]; then
    echo "🔧 建议修改应用配置使用DeepSeek："
    echo "1. 修改CopilotController使用DeepSeek API"
    echo "2. 更新模型名称为: deepseek-chat"
    echo "3. 调整API端点为: https://api.deepseek.com/"
fi

echo ""
echo "📋 下一步操作："
echo "1. 选择一个可用的AI服务"
echo "2. 修改应用代码以使用选定的服务"
echo "3. 重启应用并测试AI功能"

echo ""
echo "🔗 获取API密钥："
echo "Groq: https://console.groq.com/keys"
echo "DeepSeek: https://platform.deepseek.com/api_keys"
echo "HuggingFace: https://huggingface.co/settings/tokens"

echo ""
echo "✅ 备选AI模型测试完成！"
