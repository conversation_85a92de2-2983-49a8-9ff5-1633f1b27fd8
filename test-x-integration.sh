#!/bin/bash

echo "🧪 测试 X (Twitter) 集成配置..."

# 检查环境变量
echo "📋 检查环境变量配置..."
if docker-compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "TWITTER|X_" > /dev/null; then
    echo "✅ X 环境变量已配置"
    docker-compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "TWITTER|X_" | sed 's/=.*/=***/'
else
    echo "❌ 未找到 X 环境变量"
fi

echo ""
echo "🌐 检查回调URL可访问性..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" https://ai.guiyunai.fun/integrations/social/x

echo ""
echo "📊 检查容器状态..."
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "📝 检查应用日志（最近10行）..."
docker-compose -f docker-compose.prod.yml logs --tail=10 postiz

echo ""
echo "🎯 下一步操作指南："
echo "1. 在 X Developer Portal 中设置回调URL: https://ai.guiyunai.fun/integrations/social/x"
echo "2. 访问 https://ai.guiyunai.fun 连接您的X账户"
echo "3. 创建测试帖子验证发布功能"
