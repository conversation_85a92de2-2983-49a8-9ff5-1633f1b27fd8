#!/bin/bash

echo "🔧 修复当前Postiz配置"
echo "===================="

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo "第一步：修复Docker Compose配置..."

# 备份当前配置
cp docker-compose.prod.yml docker-compose.prod.yml.backup.$(date +%Y%m%d_%H%M%S)

# 检查并修复环境变量
echo "🔧 检查环境变量配置..."
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "UPLOAD|STORAGE|PUBLIC" | head -10

echo ""
echo "第二步：修复上传目录权限..."

# 确保上传目录存在并设置正确权限
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads
docker compose -f docker-compose.prod.yml exec -T postiz chmod 755 /uploads
docker compose -f docker-compose.prod.yml exec -T postiz chown -R node:node /uploads 2>/dev/null || echo "权限设置需要root用户"

# 创建年月日目录结构
docker compose -f docker-compose.prod.yml exec -T postiz mkdir -p /uploads/$(date +%Y/%m/%d)
docker compose -f docker-compose.prod.yml exec -T postiz chmod -R 755 /uploads/$(date +%Y)

echo ""
echo "第三步：测试文件服务..."

# 创建测试文件
docker compose -f docker-compose.prod.yml exec -T postiz sh -c 'echo "test image" > /uploads/test-image.txt'
docker compose -f docker-compose.prod.yml exec -T postiz chmod 644 /uploads/test-image.txt

# 测试直接访问
echo "🌐 测试直接文件访问..."
DIRECT_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test-image.txt)
echo "直接访问状态码: $DIRECT_CODE"

# 测试Next.js图片优化
echo "🖼️ 测试Next.js图片优化..."
ENCODED_URL=$(python3 -c "import urllib.parse; print(urllib.parse.quote('https://ai.guiyunai.fun/uploads/test-image.txt', safe=''))" 2>/dev/null || echo "https%3A//ai.guiyunai.fun/uploads/test-image.txt")
NEXTJS_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://ai.guiyunai.fun/_next/image?url=$ENCODED_URL&w=128&q=75")
echo "Next.js优化访问状态码: $NEXTJS_CODE"

echo ""
echo "第四步：重启服务应用修复..."

# 重启Postiz服务
docker compose -f docker-compose.prod.yml restart postiz

echo "⏳ 等待服务重启..."
sleep 15

# 再次测试
echo "🔄 重启后测试..."
FINAL_DIRECT=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun/uploads/test-image.txt)
FINAL_NEXTJS=$(curl -s -o /dev/null -w "%{http_code}" "https://ai.guiyunai.fun/_next/image?url=$ENCODED_URL&w=128&q=75")

echo ""
echo "🎯 修复结果："
echo "=================================="
echo "直接文件访问: $FINAL_DIRECT"
echo "Next.js优化访问: $FINAL_NEXTJS"

if [ "$FINAL_DIRECT" = "200" ] && [ "$FINAL_NEXTJS" = "200" ]; then
    echo "✅ 修复成功！图片功能应该正常工作"
elif [ "$FINAL_DIRECT" = "200" ] && [ "$FINAL_NEXTJS" != "200" ]; then
    echo "⚠️ 直接访问正常，但Next.js优化有问题"
    echo "这可能需要修改Next.js配置或使用完全重建"
else
    echo "❌ 修复失败，建议使用完全重建方案"
fi

echo ""
echo "📝 检查最新日志..."
docker compose -f docker-compose.prod.yml logs postiz --tail=10

echo ""
echo "🎯 下一步建议："
if [ "$FINAL_DIRECT" = "200" ] && [ "$FINAL_NEXTJS" = "200" ]; then
    echo "1. 在Postiz中测试图片上传功能"
    echo "2. 验证上传的图片能正常显示"
    echo "3. 如果仍有问题，检查具体的错误信息"
else
    echo "1. 运行完全重建: ./complete-rebuild.sh"
    echo "2. 或检查Nginx配置是否正确"
    echo "3. 考虑使用外部文件存储服务"
fi
