#!/bin/bash

echo "🌟 配置Cloudflare R2免费对象存储"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}使用方法：${NC}"
    echo "./configure-cloudflare-r2.sh --account-id YOUR_ACCOUNT_ID --access-key YOUR_ACCESS_KEY --secret-key YOUR_SECRET_KEY --bucket YOUR_BUCKET_NAME"
    echo ""
    echo -e "${BLUE}或者交互式配置：${NC}"
    echo "./configure-cloudflare-r2.sh --interactive"
    echo ""
    echo -e "${GREEN}请先查看 '配置Cloudflare-R2免费存储.md' 获取详细步骤${NC}"
    exit 1
fi

# 解析参数
INTERACTIVE=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --account-id)
            ACCOUNT_ID="$2"
            shift 2
            ;;
        --access-key)
            ACCESS_KEY="$2"
            shift 2
            ;;
        --secret-key)
            SECRET_KEY="$2"
            shift 2
            ;;
        --bucket)
            BUCKET_NAME="$2"
            shift 2
            ;;
        --interactive)
            INTERACTIVE=true
            shift
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 交互式配置
if [ "$INTERACTIVE" = true ]; then
    echo -e "${BLUE}交互式配置Cloudflare R2${NC}"
    echo "请按照 '配置Cloudflare-R2免费存储.md' 文档获取以下信息："
    echo ""
    
    read -p "请输入Account ID: " ACCOUNT_ID
    read -p "请输入Access Key ID: " ACCESS_KEY
    read -p "请输入Secret Access Key: " SECRET_KEY
    read -p "请输入Bucket名称: " BUCKET_NAME
fi

# 验证参数
if [ -z "$ACCOUNT_ID" ] || [ -z "$ACCESS_KEY" ] || [ -z "$SECRET_KEY" ] || [ -z "$BUCKET_NAME" ]; then
    echo -e "${RED}错误：缺少必要参数${NC}"
    echo "需要提供：--account-id, --access-key, --secret-key, --bucket"
    exit 1
fi

echo ""
echo -e "${GREEN}配置信息确认：${NC}"
echo "Account ID: $ACCOUNT_ID"
echo "Access Key: ${ACCESS_KEY:0:8}..."
echo "Secret Key: ${SECRET_KEY:0:8}..."
echo "Bucket名称: $BUCKET_NAME"
echo ""

read -p "确认配置正确？(y/N): " confirm
if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "配置已取消"
    exit 1
fi

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo -e "${BLUE}第一步：备份当前配置${NC}"
echo "======================"

# 备份当前配置
BACKUP_FILE="docker-compose.prod.yml.backup.$(date +%Y%m%d_%H%M%S)"
cp docker-compose.prod.yml "$BACKUP_FILE"
echo "✅ 配置已备份到: $BACKUP_FILE"

echo ""
echo -e "${YELLOW}第二步：更新Docker Compose配置${NC}"
echo "=================================="

# 生成Bucket URL
BUCKET_URL="https://$BUCKET_NAME.$ACCOUNT_ID.r2.cloudflarestorage.com"

# 创建新的配置
cat > temp_r2_config.yml << EOF
      # Cloudflare R2免费对象存储配置
      STORAGE_PROVIDER: "cloudflare"
      
      # Cloudflare R2配置
      CLOUDFLARE_ACCOUNT_ID: "$ACCOUNT_ID"
      CLOUDFLARE_ACCESS_KEY: "$ACCESS_KEY"
      CLOUDFLARE_SECRET_ACCESS_KEY: "$SECRET_KEY"
      CLOUDFLARE_BUCKETNAME: "$BUCKET_NAME"
      CLOUDFLARE_REGION: "auto"
      CLOUDFLARE_BUCKET_URL: "$BUCKET_URL"
      
      # 移除本地存储配置
      # UPLOAD_DIRECTORY: "/uploads"
      # NEXT_PUBLIC_UPLOAD_DIRECTORY: "/uploads"
      # NEXT_PUBLIC_UPLOAD_STATIC_DIRECTORY: "/uploads"
EOF

# 替换存储配置
echo "🔧 更新存储配置..."

# 使用sed替换存储相关配置
sed -i '/STORAGE_PROVIDER:/,/NEXT_PUBLIC_UPLOAD_URL:/c\
      # Cloudflare R2免费对象存储配置\
      STORAGE_PROVIDER: "cloudflare"\
      \
      # Cloudflare R2配置\
      CLOUDFLARE_ACCOUNT_ID: "'$ACCOUNT_ID'"\
      CLOUDFLARE_ACCESS_KEY: "'$ACCESS_KEY'"\
      CLOUDFLARE_SECRET_ACCESS_KEY: "'$SECRET_KEY'"\
      CLOUDFLARE_BUCKETNAME: "'$BUCKET_NAME'"\
      CLOUDFLARE_REGION: "auto"\
      CLOUDFLARE_BUCKET_URL: "'$BUCKET_URL'"' docker-compose.prod.yml

# 清理临时文件
rm -f temp_r2_config.yml

echo "✅ Docker Compose配置已更新"

echo ""
echo -e "${GREEN}第三步：重启服务${NC}"
echo "=================="

echo "🔄 停止当前服务..."
docker compose -f docker-compose.prod.yml down

echo "🚀 启动服务（使用Cloudflare R2）..."
docker compose -f docker-compose.prod.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo ""
echo -e "${BLUE}第四步：测试配置${NC}"
echo "=================="

# 检查服务状态
echo "📊 检查服务状态..."
docker ps --format "table {{.Names}}\t{{.Status}}" | grep postiz

# 检查环境变量
echo "📋 检查Cloudflare R2配置..."
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "CLOUDFLARE|STORAGE" | head -5

# 测试网站访问
echo "🌐 测试网站访问..."
MAIN_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun 2>/dev/null || echo "000")
echo "网站状态码: $MAIN_CODE"

echo ""
echo -e "${GREEN}🎉 配置完成！${NC}"
echo "================"

echo "✅ Cloudflare R2已配置完成"
echo "✅ 免费10GB存储空间"
echo "✅ 无限下载流量"
echo "✅ 全球CDN加速"

echo ""
echo -e "${YELLOW}下一步操作：${NC}"
echo "1. 访问 https://ai.guiyunai.fun"
echo "2. 测试图片上传功能"
echo "3. 验证图片显示正常"
echo "4. 享受免费的高速图片存储！"

echo ""
echo -e "${BLUE}配置信息：${NC}"
echo "Bucket URL: $BUCKET_URL"
echo "备份文件: $BACKUP_FILE"

if [ "$MAIN_CODE" = "200" ]; then
    echo ""
    echo -e "${GREEN}🎊 恭喜！服务运行正常，可以开始使用了！${NC}"
else
    echo ""
    echo -e "${YELLOW}⚠️ 服务可能需要更多时间启动，请稍等几分钟后再测试${NC}"
fi

echo ""
echo "📚 如需帮助，请查看："
echo "- 配置Cloudflare-R2免费存储.md"
echo "- Docker日志: docker compose -f docker-compose.prod.yml logs postiz"
