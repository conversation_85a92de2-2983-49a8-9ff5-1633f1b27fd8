#!/bin/bash

echo "🔍 云归.中国 Postiz 服务状态检查"
echo "=================================="

cd /www/wwwroot/ai.guiyunai.fun

echo ""
echo "📊 Docker容器状态："
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "🌐 网站访问测试："
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
echo "状态码: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 网站正常访问"
elif [ "$HTTP_CODE" = "502" ]; then
    echo "❌ 502错误 - 后端服务问题"
    echo "🔧 尝试重启服务..."
    docker compose -f docker-compose.prod.yml restart
    echo "⏳ 等待服务启动..."
    sleep 20
    NEW_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://ai.guiyunai.fun)
    echo "重启后状态码: $NEW_CODE"
else
    echo "⚠️ 其他错误: $HTTP_CODE"
fi

echo ""
echo "🔑 X API配置检查："
docker compose -f docker-compose.prod.yml exec -T postiz printenv | grep -E "X_API" | sed 's/=.*/=***/'

echo ""
echo "📝 最近日志（最后10行）："
docker compose -f docker-compose.prod.yml logs postiz --tail=10

echo ""
echo "🎯 下一步操作："
echo "1. 如果网站正常(200)，访问: https://ai.guiyunai.fun"
echo "2. 如果仍有502错误，检查日志中的具体错误信息"
echo "3. 完成X Developer Portal回调URL设置"
echo "4. 在Postiz中连接X账户"
